import React from "react";
import { useAuth } from "../../contexts/AuthContext";
import { useSidebar } from "../../contexts/SidebarContext";
import { Menu } from "lucide-react";
import { Avatar, Button } from "antd";

const Header = ({
  title = "Component Librarys",
  subtitle = "Build and manage reusable components for your websites",
}) => {
  const { user } = useAuth();
  const { isMobile, toggleMobileMenu } = useSidebar();

  return (
    <div className="tw-bg-white tw-shadow-sm tw-border-b tw-border-gray-200 tw-px-6 tw-py-3">
      <div className="tw-flex tw-items-center tw-justify-between">
        <div className="tw-flex tw-items-center">
          {/* Mobile Menu Button */}
          {isMobile && (
            <Button
              type="text"
              icon={<Menu size={20} />}
              onClick={toggleMobileMenu}
              className="tw-mr-4 tw-p-1 tw-flex tw-items-center tw-justify-center"
              style={{
                width: "40px",
                height: "40px",
                border: "none",
                boxShadow: "none",
              }}
            />
          )}

          <div>
            <h1 className="!tw-text-2xl tw-font-bold tw-text-gray-900">
              {title}
            </h1>
            {subtitle && (
              <p className="tw-text-sm tw-text-gray-600 tw-mt-1">{subtitle}</p>
            )}
          </div>
        </div>

        <div className="tw-flex tw-items-center tw-space-x-4">
          <Avatar className="!tw-bg-[#374151]" size={40}>
            {user?.username?.charAt(0).toUpperCase()}
          </Avatar>
        </div>
      </div>
    </div>
  );
};

export default Header;
