import express from "express";
import { db } from "../database.js";
import { authenticateToken } from "./auth.js";

const router = express.Router();

// Get all websites
router.get("/", authenticateToken, (req, res) => {
  const query = `
    SELECT w.*, t.name as template_name
    FROM websites w
    LEFT JOIN templates t ON w.template_id = t.id
    ORDER BY w.name
  `;

  db.all(query, (err, websites) => {
    if (err) {
      return res.status(500).json({ error: "Database error" });
    }

    const websitesWithParsedData = websites.map((website) => ({
      ...website,
      content_data: website.content_data
        ? JSON.parse(website.content_data)
        : {},
    }));

    res.json(websitesWithParsedData);
  });
});

// Get website by ID
router.get("/:id", authenticateToken, (req, res) => {
  const { id } = req.params;

  const query = `
    SELECT w.*, t.name as template_name
    FROM websites w
    LEFT JOIN templates t ON w.template_id = t.id
    WHERE w.id = ?
  `;

  db.get(query, [id], (err, website) => {
    if (err) {
      return res.status(500).json({ error: "Database error" });
    }
    if (!website) {
      return res.status(404).json({ error: "Website not found" });
    }

    website.content_data = website.content_data
      ? JSON.parse(website.content_data)
      : {};
    res.json(website);
  });
});

// Create website
router.post("/", authenticateToken, (req, res) => {
  const {
    name,
    template_id,
    primary_color,
    secondary_color,
    logo_url,
    content_data,
  } = req.body;

  db.run(
    "INSERT INTO websites (name, template_id, primary_color, secondary_color, logo_url, content_data) VALUES (?, ?, ?, ?, ?, ?)",
    [
      name,
      template_id,
      primary_color || "#3B82F6",
      secondary_color || "#8B5CF6",
      logo_url,
      JSON.stringify(content_data || {}),
    ],
    function (err) {
      if (err) {
        return res.status(500).json({ error: "Failed to create website" });
      }

      db.get(
        "SELECT * FROM websites WHERE id = ?",
        [this.lastID],
        (err, website) => {
          if (err) {
            return res.status(500).json({ error: "Database error" });
          }
          website.content_data = website.content_data
            ? JSON.parse(website.content_data)
            : {};
          res.json(website);
        }
      );
    }
  );
});

// Update website
router.put("/:id", authenticateToken, (req, res) => {
  const { id } = req.params;
  const {
    name,
    template_id,
    primary_color,
    secondary_color,
    logo_url,
    content_data,
    status,
  } = req.body;

  db.run(
    "UPDATE websites SET name = ?, template_id = ?, primary_color = ?, secondary_color = ?, logo_url = ?, content_data = ?, status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?",
    [
      name,
      template_id,
      primary_color,
      secondary_color,
      logo_url,
      JSON.stringify(content_data || {}),
      status,
      id,
    ],
    function (err) {
      if (err) {
        return res.status(500).json({ error: "Failed to update website" });
      }

      db.get("SELECT * FROM websites WHERE id = ?", [id], (err, website) => {
        if (err) {
          return res.status(500).json({ error: "Database error" });
        }
        website.content_data = website.content_data
          ? JSON.parse(website.content_data)
          : {};
        res.json(website);
      });
    }
  );
});

// Delete website
router.delete("/:id", authenticateToken, (req, res) => {
  const { id } = req.params;

  db.run("DELETE FROM websites WHERE id = ?", [id], function (err) {
    if (err) {
      return res.status(500).json({ error: "Failed to delete website" });
    }
    res.json({ message: "Website deleted successfully" });
  });
});

export default router;
