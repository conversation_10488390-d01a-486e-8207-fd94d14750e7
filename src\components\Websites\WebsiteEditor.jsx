import React, { useState, useEffect } from "react";
import { Radio } from "antd";
import Header from "../Layout/Header";
import {
  Save,
  X,
  Upload,
  Palette,
  FileText,
  Eye,
  Settings,
} from "lucide-react";
import useHttp from "../../hooks/use-http";
import { CONSTANTS } from "../../util/constant/CONSTANTS";
import { apiGenerator } from "../../util/functions";

const WebsiteEditor = ({ website, templates, onSave, onCancel }) => {
  const api = useHttp();
  const [formData, setFormData] = useState({
    name: "",
    template_id: "",
    primary_color: "#3B82F6",
    secondary_color: "#8B5CF6",
    logo_url: "",
    content_data: {},
    status: "draft",
  });
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [availablePlaceholders, setAvailablePlaceholders] = useState([]);
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState("basic");

  useEffect(() => {
    if (website) {
      setFormData({
        name: website.name || "",
        template_id: website.template_id || "",
        primary_color: website.primary_color || "#3B82F6",
        secondary_color: website.secondary_color || "#8B5CF6",
        logo_url: website.logo_url || "",
        content_data: website.content_data || {},
        status: website.status || "draft",
      });

      if (website.template_id) {
        const template = templates.find((t) => t.id === website.template_id);
        setSelectedTemplate(template);
        if (template) {
          fetchTemplatePlaceholders(template);
        }
      }
    }
  }, [website, templates]);

  const fetchTemplatePlaceholders = async (template) => {
    try {
      // Get all pages in the template
      const pageIds = template.pages || [];
      if (pageIds.length === 0) {
        setAvailablePlaceholders([]);
        return;
      }

      // Fetch page details using bulk request
      const pageRequests = pageIds.map((pageId) => ({
        ...CONSTANTS.API.pages.getById(pageId),
      }));

      api.sendBulkRequest(pageRequests, (pages) => {
        // Get all components used in these pages
        const componentIds = new Set();
        pages.forEach((page) => {
          if (page.components) {
            page.components.forEach((comp) => componentIds.add(comp.id));
          }
        });

        if (componentIds.size === 0) {
          setAvailablePlaceholders([]);
          return;
        }

        // Fetch component details using bulk request
        const componentRequests = Array.from(componentIds).map(
          (componentId) => ({
            ...apiGenerator(CONSTANTS.API.components.getById, {
              id: componentId,
            }),
          })
        );

        api.sendBulkRequest(componentRequests, (components) => {
          // Collect all unique placeholders
          const placeholders = new Set();
          components.forEach((component) => {
            if (component.placeholders) {
              component.placeholders.forEach((placeholder) =>
                placeholders.add(placeholder)
              );
            }
          });

          setAvailablePlaceholders(Array.from(placeholders).sort());
        });
      });
    } catch (error) {
      console.error("Error fetching template placeholders:", error);
      setAvailablePlaceholders([]);
    }
  };

  const handleTemplateChange = (templateId) => {
    const template = templates.find((t) => t.id === parseInt(templateId));
    setSelectedTemplate(template);
    setFormData({
      ...formData,
      template_id: templateId,
      content_data: {}, // Reset content data when template changes
    });

    if (template) {
      fetchTemplatePlaceholders(template);
    } else {
      setAvailablePlaceholders([]);
    }
  };

  const handleContentChange = (placeholder, value) => {
    setFormData({
      ...formData,
      content_data: {
        ...formData.content_data,
        [placeholder]: value,
      },
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSaving(true);

    const apiConfig = website
      ? apiGenerator(CONSTANTS.API.websites.update, { id: website.id })
      : CONSTANTS.API.websites.create;

    api.sendRequest(
      apiConfig,
      (res) => {
        console.log("Website saved successfully:", res);
        setSaving(false);
        onSave();
      },
      formData,
      website
        ? "Website updated successfully!"
        : "Website created successfully!",
      (error) => {
        console.error("Error saving website:", error);
        setSaving(false);
      }
    );
  };

  const predefinedColors = [
    "#3B82F6",
    "#8B5CF6",
    "#10B981",
    "#F59E0B",
    "#EF4444",
    "#06B6D4",
    "#84CC16",
    "#F97316",
    "#EC4899",
    "#6366F1",
    "#14B8A6",
    "#F97316",
  ];

  const getPlaceholderType = (placeholder) => {
    const lowerPlaceholder = placeholder.toLowerCase();

    if (
      lowerPlaceholder.includes("image") ||
      lowerPlaceholder.includes("img") ||
      lowerPlaceholder.includes("logo")
    ) {
      return "url";
    } else if (lowerPlaceholder.includes("email")) {
      return "email";
    } else if (
      lowerPlaceholder.includes("phone") ||
      lowerPlaceholder.includes("tel")
    ) {
      return "tel";
    } else if (
      lowerPlaceholder.includes("url") ||
      lowerPlaceholder.includes("link")
    ) {
      return "url";
    } else if (
      lowerPlaceholder.includes("content") ||
      lowerPlaceholder.includes("description") ||
      lowerPlaceholder.includes("text")
    ) {
      return "textarea";
    }
    return "text";
  };

  return (
    <>
      <Header
        title={website ? "Edit Website" : "Create Website"}
        subtitle="Configure your website settings and content"
      />

      <div className="tw-p-6">
        <form onSubmit={handleSubmit} className="tw-max-w-6xl tw-mx-auto">
          {/* Tab Navigation */}
          <div className="tw-bg-white tw-rounded-xl tw-shadow-sm tw-border tw-border-gray-200 tw-p-4 tw-mb-6">
            <Radio.Group
              value={activeTab}
              onChange={(e) => setActiveTab(e.target.value)}
              buttonStyle="solid"
              size="large"
              className="tw-w-full"
            >
              <Radio.Button
                value="basic"
                className="tw-flex-1 tw-text-center"
                style={{ width: "33.33%" }}
              >
                <div className="tw-flex tw-items-center tw-justify-center">
                  <Settings className="tw-w-4 tw-h-4 tw-mr-2" />
                  Basic Settings
                </div>
              </Radio.Button>
              <Radio.Button
                value="design"
                className="tw-flex-1 tw-text-center"
                style={{ width: "33.33%" }}
              >
                <div className="tw-flex tw-items-center tw-justify-center">
                  <Palette className="tw-w-4 tw-h-4 tw-mr-2" />
                  Design & Branding
                </div>
              </Radio.Button>
              <Radio.Button
                value="content"
                className="tw-flex-1 tw-text-center"
                style={{ width: "33.33%" }}
              >
                <div className="tw-flex tw-items-center tw-justify-center">
                  <FileText className="tw-w-4 tw-h-4 tw-mr-2" />
                  Content Management
                </div>
              </Radio.Button>
            </Radio.Group>
          </div>

          {/* Basic Settings Tab */}
          {activeTab === "basic" && (
            <div className="tw-grid tw-grid-cols-1 tw-lg:tw-grid-cols-2 tw-gap-8">
              <div className="tw-bg-white tw-rounded-xl tw-shadow-sm tw-border tw-border-gray-200 tw-p-6">
                <h3 className="tw-text-lg tw-font-semibold tw-text-gray-900 tw-mb-4">
                  Website Information
                </h3>

                <div className="tw-space-y-4">
                  <div>
                    <label className="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-2">
                      Website Name
                    </label>
                    <input
                      type="text"
                      value={formData.name}
                      onChange={(e) =>
                        setFormData({ ...formData, name: e.target.value })
                      }
                      className="tw-w-full tw-px-3 tw-py-2 tw-border tw-border-gray-300 tw-rounded-lg tw-focus:tw-ring-2 tw-focus:tw-ring-blue-500 tw-focus:tw-border-transparent"
                      placeholder="e.g., My Business Website"
                      required
                    />
                  </div>

                  <div>
                    <label className="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-2">
                      Template
                    </label>
                    <select
                      value={formData.template_id}
                      onChange={(e) => handleTemplateChange(e.target.value)}
                      className="tw-w-full tw-px-3 tw-py-2 tw-border tw-border-gray-300 tw-rounded-lg tw-focus:tw-ring-2 tw-focus:tw-ring-blue-500 tw-focus:tw-border-transparent"
                      required
                    >
                      <option value="">Select a template</option>
                      {templates.map((template) => (
                        <option key={template.id} value={template.id}>
                          {template.name} (
                          {template.pages ? template.pages.length : 0} pages)
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-2">
                      Status
                    </label>
                    <select
                      value={formData.status}
                      onChange={(e) =>
                        setFormData({ ...formData, status: e.target.value })
                      }
                      className="tw-w-full tw-px-3 tw-py-2 tw-border tw-border-gray-300 tw-rounded-lg tw-focus:tw-ring-2 tw-focus:tw-ring-blue-500 tw-focus:tw-border-transparent"
                    >
                      <option value="draft">Draft</option>
                      <option value="published">Published</option>
                    </select>
                  </div>
                </div>
              </div>

              {selectedTemplate && (
                <div className="tw-bg-white tw-rounded-xl tw-shadow-sm tw-border tw-border-gray-200 tw-p-6">
                  <h3 className="tw-text-lg tw-font-semibold tw-text-gray-900 tw-mb-4">
                    Template Details
                  </h3>

                  <div className="tw-space-y-3">
                    <div>
                      <p className="tw-text-sm tw-font-medium tw-text-gray-700">
                        Template: {selectedTemplate.name}
                      </p>
                      <p className="tw-text-sm tw-text-gray-600">
                        {selectedTemplate.description ||
                          "No description available"}
                      </p>
                    </div>

                    <div>
                      <p className="tw-text-sm tw-font-medium tw-text-gray-700">
                        Pages:{" "}
                        {selectedTemplate.pages
                          ? selectedTemplate.pages.length
                          : 0}
                      </p>
                    </div>

                    <div>
                      <p className="tw-text-sm tw-font-medium tw-text-gray-700">
                        Content Fields: {availablePlaceholders.length}
                      </p>
                      {availablePlaceholders.length > 0 && (
                        <div className="tw-flex tw-flex-wrap tw-gap-1 tw-mt-2">
                          {availablePlaceholders
                            .slice(0, 6)
                            .map((placeholder) => (
                              <span
                                key={placeholder}
                                className="tw-px-2 tw-py-1 tw-bg-blue-100 tw-text-blue-800 tw-text-xs tw-rounded-full"
                              >
                                {placeholder}
                              </span>
                            ))}
                          {availablePlaceholders.length > 6 && (
                            <span className="tw-px-2 tw-py-1 tw-bg-gray-100 tw-text-gray-600 tw-text-xs tw-rounded-full">
                              +{availablePlaceholders.length - 6} more
                            </span>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Design & Branding Tab */}
          {activeTab === "design" && (
            <div className="tw-grid tw-grid-cols-1 tw-lg:tw-grid-cols-2 tw-gap-8">
              <div className="tw-bg-white tw-rounded-xl tw-shadow-sm tw-border tw-border-gray-200 tw-p-6">
                <h3 className="tw-text-lg tw-font-semibold tw-text-gray-900 tw-mb-4">
                  Brand Colors
                </h3>

                <div className="tw-space-y-6">
                  <div>
                    <label className="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-2">
                      Primary Color
                    </label>
                    <div className="tw-flex tw-space-x-2 tw-mb-3">
                      {predefinedColors.slice(0, 6).map((color) => (
                        <button
                          key={color}
                          type="button"
                          onClick={() =>
                            setFormData({ ...formData, primary_color: color })
                          }
                          className={`tw-w-8 tw-h-8 tw-rounded-full tw-border-2 tw-transition-all ${
                            formData.primary_color === color
                              ? "tw-border-gray-900 tw-scale-110"
                              : "tw-border-gray-300 tw-hover:tw-scale-105"
                          }`}
                          style={{ backgroundColor: color }}
                        />
                      ))}
                    </div>
                    <input
                      type="color"
                      value={formData.primary_color}
                      onChange={(e) =>
                        setFormData({
                          ...formData,
                          primary_color: e.target.value,
                        })
                      }
                      className="tw-w-full tw-h-10 tw-rounded-lg tw-border tw-border-gray-300"
                    />
                  </div>

                  <div>
                    <label className="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-2">
                      Secondary Color
                    </label>
                    <div className="tw-flex tw-space-x-2 tw-mb-3">
                      {predefinedColors.slice(6).map((color) => (
                        <button
                          key={color}
                          type="button"
                          onClick={() =>
                            setFormData({ ...formData, secondary_color: color })
                          }
                          className={`tw-w-8 tw-h-8 tw-rounded-full tw-border-2 tw-transition-all ${
                            formData.secondary_color === color
                              ? "tw-border-gray-900 tw-scale-110"
                              : "tw-border-gray-300 tw-hover:tw-scale-105"
                          }`}
                          style={{ backgroundColor: color }}
                        />
                      ))}
                    </div>
                    <input
                      type="color"
                      value={formData.secondary_color}
                      onChange={(e) =>
                        setFormData({
                          ...formData,
                          secondary_color: e.target.value,
                        })
                      }
                      className="tw-w-full tw-h-10 tw-rounded-lg tw-border tw-border-gray-300"
                    />
                  </div>
                </div>
              </div>

              <div className="tw-bg-white tw-rounded-xl tw-shadow-sm tw-border tw-border-gray-200 tw-p-6">
                <h3 className="tw-text-lg tw-font-semibold tw-text-gray-900 tw-mb-4">
                  Logo & Assets
                </h3>

                <div className="tw-space-y-4">
                  <div>
                    <label className="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-2">
                      Logo URL
                    </label>
                    <input
                      type="url"
                      value={formData.logo_url}
                      onChange={(e) =>
                        setFormData({ ...formData, logo_url: e.target.value })
                      }
                      className="tw-w-full tw-px-3 tw-py-2 tw-border tw-border-gray-300 tw-rounded-lg tw-focus:tw-ring-2 tw-focus:tw-ring-blue-500 tw-focus:tw-border-transparent"
                      placeholder="https://example.com/logo.png"
                    />
                  </div>

                  {formData.logo_url && (
                    <div className="tw-mt-3">
                      <p className="tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-2">
                        Logo Preview
                      </p>
                      <div className="tw-border tw-border-gray-200 tw-rounded-lg tw-p-4 tw-bg-gray-50">
                        <img
                          src={formData.logo_url}
                          alt="Logo Preview"
                          className="tw-h-16 tw-w-auto tw-object-contain"
                          onError={(e) => {
                            e.target.style.display = "none";
                            e.target.nextSibling.style.display = "block";
                          }}
                        />
                        <div
                          className="tw-text-sm tw-text-red-500"
                          style={{ display: "none" }}
                        >
                          Failed to load logo
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Color Preview */}
                <div className="tw-mt-6">
                  <p className="tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-2">
                    Color Preview
                  </p>
                  <div className="tw-border tw-border-gray-200 tw-rounded-lg tw-p-4 tw-bg-gray-50">
                    <div className="tw-flex tw-space-x-4">
                      <div className="tw-flex tw-items-center">
                        <div
                          className="tw-w-8 tw-h-8 tw-rounded-full tw-mr-2"
                          style={{ backgroundColor: formData.primary_color }}
                        />
                        <div>
                          <p
                            className="tw-text-sm tw-font-medium"
                            style={{ color: formData.primary_color }}
                          >
                            Primary Color
                          </p>
                          <p className="tw-text-xs tw-text-gray-500">
                            {formData.primary_color}
                          </p>
                        </div>
                      </div>
                      <div className="tw-flex tw-items-center">
                        <div
                          className="tw-w-8 tw-h-8 tw-rounded-full tw-mr-2"
                          style={{ backgroundColor: formData.secondary_color }}
                        />
                        <div>
                          <p
                            className="tw-text-sm tw-font-medium"
                            style={{ color: formData.secondary_color }}
                          >
                            Secondary Color
                          </p>
                          <p className="tw-text-xs tw-text-gray-500">
                            {formData.secondary_color}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Content Management Tab */}
          {activeTab === "content" && (
            <div className="tw-bg-white tw-rounded-xl tw-shadow-sm tw-border tw-border-gray-200 tw-p-6">
              <h3 className="tw-text-lg tw-font-semibold tw-text-gray-900 tw-mb-4">
                Content Fields
              </h3>

              {availablePlaceholders.length > 0 ? (
                <div className="tw-space-y-4">
                  {availablePlaceholders.map((placeholder) => {
                    const inputType = getPlaceholderType(placeholder);

                    return (
                      <div key={placeholder}>
                        <label className="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-2 tw-capitalize">
                          {placeholder.replace(/_/g, " ")}
                        </label>

                        {inputType === "textarea" ? (
                          <textarea
                            value={formData.content_data[placeholder] || ""}
                            onChange={(e) =>
                              handleContentChange(placeholder, e.target.value)
                            }
                            className="tw-w-full tw-px-3 tw-py-2 tw-border tw-border-gray-300 tw-rounded-lg tw-focus:tw-ring-2 tw-focus:tw-ring-blue-500 tw-focus:tw-border-transparent tw-h-24"
                            placeholder={`Enter ${placeholder.replace(
                              /_/g,
                              " "
                            )}`}
                            rows="3"
                          />
                        ) : (
                          <input
                            type={inputType}
                            value={formData.content_data[placeholder] || ""}
                            onChange={(e) =>
                              handleContentChange(placeholder, e.target.value)
                            }
                            className="tw-w-full tw-px-3 tw-py-2 tw-border tw-border-gray-300 tw-rounded-lg tw-focus:tw-ring-2 tw-focus:tw-ring-blue-500 tw-focus:tw-border-transparent"
                            placeholder={`Enter ${placeholder.replace(
                              /_/g,
                              " "
                            )}`}
                          />
                        )}
                      </div>
                    );
                  })}
                </div>
              ) : (
                <div className="tw-text-center tw-py-8">
                  <FileText className="tw-w-12 tw-h-12 tw-text-gray-300 tw-mx-auto tw-mb-3" />
                  <p className="tw-text-gray-500 tw-text-sm">
                    {formData.template_id
                      ? "No content fields found in the selected template"
                      : "Select a template to see content fields"}
                  </p>
                </div>
              )}
            </div>
          )}

          {/* Action Buttons */}
          <div className="tw-flex tw-justify-end tw-space-x-4 tw-mt-8 tw-pt-6 tw-border-t tw-border-gray-200">
            <button
              type="button"
              onClick={onCancel}
              className="tw-px-6 tw-py-2 tw-border tw-border-gray-300 tw-text-gray-700 tw-rounded-lg tw-font-medium tw-hover:tw-bg-gray-50 tw-transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={saving || !formData.name || !formData.template_id}
              className="tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 tw-text-white tw-px-6 tw-py-2 tw-rounded-lg tw-font-medium tw-hover:tw-from-blue-700 tw-hover:tw-to-purple-700 tw-transition-all tw-flex tw-items-center tw-disabled:tw-opacity-50"
            >
              {saving ? (
                <>
                  <div className="tw-animate-spin tw-rounded-full tw-h-4 tw-w-4 tw-border-b-2 tw-border-white tw-mr-2"></div>
                  Saving...
                </>
              ) : (
                <>
                  <Save className="tw-w-4 tw-h-4 tw-mr-2" />
                  {website ? "Update Website" : "Create Website"}
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </>
  );
};

export default WebsiteEditor;
