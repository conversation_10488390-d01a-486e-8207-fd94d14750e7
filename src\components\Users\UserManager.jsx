import React, { useState, useEffect } from "react";
import Sidebar from "../Layout/Sidebar";
import Header from "../Layout/Header";
import {
  Plus,
  Users,
  Edit2,
  Trash2,
  Shield,
  User,
  Save,
  X,
} from "lucide-react";
import useHttp from "../../hooks/use-http";
import { CONSTANTS } from "../../util/constant/CONSTANTS";
import { apiGenerator } from "../../util/functions";

const UserManager = () => {
  const [users, setUsers] = useState([]);
  const [showForm, setShowForm] = useState(false);
  const [formData, setFormData] = useState({
    username: "",
    email: "",
    password: "",
    role: "content_team",
  });
  const api = useHttp();

  useEffect(() => {
    // Fetch users
    api.sendRequest(CONSTANTS.API.users.get, (res) => {
      console.log("Users fetched:", res);
      setUsers(res);
    });
  }, []);

  const refreshUsers = () => {
    api.sendRequest(CONSTANTS.API.users.get, (res) => {
      setUsers(res);
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    api.sendRequest(
      CONSTANTS.API.auth.register,
      (res) => {
        console.log("User created successfully:", res);
        refreshUsers();
        resetForm();
      },
      formData,
      "User created successfully!",
      (error) => {
        console.error("Error creating user:", error);
      }
    );
  };

  const resetForm = () => {
    setFormData({
      username: "",
      email: "",
      password: "",
      role: "content_team",
    });
    setShowForm(false);
  };

  const getRoleBadgeColor = (role) => {
    switch (role) {
      case "admin":
        return "tw-bg-red-100 tw-text-red-800";
      case "content_team":
        return "tw-bg-blue-100 tw-text-blue-800";
      default:
        return "tw-bg-gray-100 tw-text-gray-800";
    }
  };

  const getRoleIcon = (role) => {
    return role === "admin" ? Shield : User;
  };

  if (api.isLoading) {
    return (
      <div className="">
        <div className="tw-animate-pulse tw-p-6">
          <div className="tw-h-8 tw-bg-gray-300 tw-rounded tw-mb-4"></div>
          <div className="tw-space-y-4">
            {[1, 2, 3, 4, 5].map((i) => (
              <div key={i} className="tw-h-20 tw-bg-gray-300 tw-rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="tw-p-6">
      <div className="tw-flex tw-justify-between tw-items-center tw-mb-6">
        <div className="tw-flex tw-items-center">
          <Users className="tw-w-6 tw-h-6 tw-text-blue-600 tw-mr-2" />
          <h2 className="tw-text-xl tw-font-semibold tw-text-gray-900">
            Users ({users.length})
          </h2>
        </div>

        <button
          onClick={() => setShowForm(true)}
          className="tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 tw-text-white tw-px-4 tw-py-2 tw-rounded-lg tw-font-medium tw-hover:tw-from-blue-700 tw-hover:tw-to-purple-700 tw-transition-all tw-flex tw-items-center"
        >
          <Plus className="tw-w-4 tw-h-4 tw-mr-2" />
          Add User
        </button>
      </div>

      {/* User Form Modal */}
      {showForm && (
        <div className="tw-fixed tw-inset-0 tw-bg-black tw-bg-opacity-50 tw-flex tw-items-center tw-justify-center tw-z-50">
          <div className="tw-bg-white tw-rounded-xl tw-p-6 tw-w-full tw-max-w-md tw-m-4">
            <div className="tw-flex tw-justify-between tw-items-center tw-mb-4">
              <h3 className="tw-text-lg tw-font-semibold tw-text-gray-900">
                Add New User
              </h3>
              <button
                onClick={resetForm}
                className="tw-text-gray-400 tw-hover:tw-text-gray-600"
              >
                <X className="tw-w-5 tw-h-5" />
              </button>
            </div>

            <form onSubmit={handleSubmit} className="tw-space-y-4">
              <div>
                <label className="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-2">
                  Username
                </label>
                <input
                  type="text"
                  value={formData.username}
                  onChange={(e) =>
                    setFormData({ ...formData, username: e.target.value })
                  }
                  className="tw-w-full tw-px-3 tw-py-2 tw-border tw-border-gray-300 tw-rounded-lg tw-focus:tw-ring-2 tw-focus:tw-ring-blue-500 tw-focus:tw-border-transparent"
                  placeholder="Enter username"
                  required
                />
              </div>

              <div>
                <label className="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-2">
                  Email
                </label>
                <input
                  type="email"
                  value={formData.email}
                  onChange={(e) =>
                    setFormData({ ...formData, email: e.target.value })
                  }
                  className="tw-w-full tw-px-3 tw-py-2 tw-border tw-border-gray-300 tw-rounded-lg tw-focus:tw-ring-2 tw-focus:tw-ring-blue-500 tw-focus:tw-border-transparent"
                  placeholder="Enter email address"
                  required
                />
              </div>

              <div>
                <label className="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-2">
                  Password
                </label>
                <input
                  type="password"
                  value={formData.password}
                  onChange={(e) =>
                    setFormData({ ...formData, password: e.target.value })
                  }
                  className="tw-w-full tw-px-3 tw-py-2 tw-border tw-border-gray-300 tw-rounded-lg tw-focus:tw-ring-2 tw-focus:tw-ring-blue-500 tw-focus:tw-border-transparent"
                  placeholder="Enter password"
                  required
                  minLength="6"
                />
              </div>

              <div>
                <label className="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-2">
                  Role
                </label>
                <select
                  value={formData.role}
                  onChange={(e) =>
                    setFormData({ ...formData, role: e.target.value })
                  }
                  className="tw-w-full tw-px-3 tw-py-2 tw-border tw-border-gray-300 tw-rounded-lg tw-focus:tw-ring-2 tw-focus:tw-ring-blue-500 tw-focus:tw-border-transparent"
                >
                  <option value="content_team">Content Team</option>
                  <option value="admin">Admin</option>
                </select>
              </div>

              <div className="tw-flex tw-space-x-3 tw-pt-4">
                <button
                  type="submit"
                  className="tw-flex-1 tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 tw-text-white tw-py-2 tw-px-4 tw-rounded-lg tw-font-medium tw-hover:tw-from-blue-700 tw-hover:tw-to-purple-700 tw-transition-all tw-flex tw-items-center tw-justify-center"
                >
                  <Save className="tw-w-4 tw-h-4 tw-mr-2" />
                  Create User
                </button>
                <button
                  type="button"
                  onClick={resetForm}
                  className="tw-flex-1 tw-bg-gray-300 tw-text-gray-700 tw-py-2 tw-px-4 tw-rounded-lg tw-font-medium tw-hover:tw-bg-gray-400 tw-transition-colors"
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Users List */}
      <div className="tw-bg-white tw-rounded-xl tw-shadow-sm tw-border tw-border-gray-200 tw-overflow-hidden">
        <div className="tw-overflow-x-auto">
          <table className="tw-w-full tw-divide-y tw-divide-gray-200">
            <thead className="tw-bg-gray-50">
              <tr>
                <th className="tw-px-6 tw-py-3 tw-text-left tw-text-xs tw-font-medium tw-text-gray-500 tw-uppercase tw-tracking-wider">
                  User
                </th>
                <th className="tw-px-6 tw-py-3 tw-text-left tw-text-xs tw-font-medium tw-text-gray-500 tw-uppercase tw-tracking-wider">
                  Role
                </th>
                <th className="tw-px-6 tw-py-3 tw-text-left tw-text-xs tw-font-medium tw-text-gray-500 tw-uppercase tw-tracking-wider">
                  Created
                </th>
                <th className="tw-px-6 tw-py-3 tw-text-right tw-text-xs tw-font-medium tw-text-gray-500 tw-uppercase tw-tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="tw-bg-white tw-divide-y tw-divide-gray-200">
              {users.map((user) => {
                const RoleIcon = getRoleIcon(user.role);

                return (
                  <tr
                    key={user.id}
                    className="tw-hover:tw-bg-gray-50 tw-transition-colors"
                  >
                    <td className="tw-px-6 tw-py-4 tw-whitespace-nowrap">
                      <div className="tw-flex tw-items-center">
                        <div className="tw-bg-gray-300 tw-w-10 tw-h-10 tw-rounded-full tw-flex tw-items-center tw-justify-center tw-mr-3">
                          <span className="tw-text-sm tw-font-medium tw-text-gray-700">
                            {user.username.charAt(0).toUpperCase()}
                          </span>
                        </div>
                        <div>
                          <div className="tw-text-sm tw-font-medium tw-text-gray-900">
                            {user.username}
                          </div>
                          <div className="tw-text-sm tw-text-gray-500">
                            {user.email}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="tw-px-6 tw-py-4 tw-whitespace-nowrap">
                      <div className="tw-flex tw-items-center">
                        <RoleIcon className="tw-w-4 tw-h-4 tw-mr-2 tw-text-gray-400" />
                        <span
                          className={`tw-inline-flex tw-px-2 tw-py-1 tw-text-xs tw-font-semibold tw-rounded-full ${getRoleBadgeColor(
                            user.role
                          )}`}
                        >
                          {user.role === "content_team"
                            ? "Content Team"
                            : "Admin"}
                        </span>
                      </div>
                    </td>
                    <td className="tw-px-6 tw-py-4 tw-whitespace-nowrap tw-text-sm tw-text-gray-500">
                      {new Date(user.created_at).toLocaleDateString()}
                    </td>
                    <td className="tw-px-6 tw-py-4 tw-whitespace-nowrap tw-text-right tw-text-sm tw-font-medium">
                      <div className="tw-flex tw-items-center tw-justify-end tw-space-x-2">
                        <button className="tw-text-blue-600 tw-hover:tw-text-blue-900 tw-p-1">
                          <Edit2 className="tw-w-4 tw-h-4" />
                        </button>
                        {user.role !== "admin" && (
                          <button className="tw-text-red-600 tw-hover:tw-text-red-900 tw-p-1">
                            <Trash2 className="tw-w-4 tw-h-4" />
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>

      {users.length === 0 && (
        <div className="tw-text-center tw-py-12">
          <Users className="tw-w-16 tw-h-16 tw-text-gray-300 tw-mx-auto tw-mb-4" />
          <h3 className="tw-text-lg tw-font-medium tw-text-gray-900 tw-mb-2">
            No users found
          </h3>
          <p className="tw-text-gray-500 tw-mb-4">
            Get started by creating your first user account
          </p>
          <button
            onClick={() => setShowForm(true)}
            className="tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 tw-text-white tw-px-6 tw-py-2 tw-rounded-lg tw-font-medium tw-hover:tw-from-blue-700 tw-hover:tw-to-purple-700 tw-transition-all"
          >
            Create First User
          </button>
        </div>
      )}
    </div>
  );
};

export default UserManager;
