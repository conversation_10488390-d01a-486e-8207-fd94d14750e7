
export const CONSTANTS = {
    API: {
        // Categories API endpoints
        categories: {
            get: {
                type: "GET",
                endpoint: "/categories"
            },
            create: {
                type: "POST",
                endpoint: "/categories"
            },
            update: {
                type: "PUT",
                endpoint: `/categories/:id`
            },
            delete: {
                type: "DELETE",
                endpoint: "/categories/:id"
            }
        },

        // Components API endpoints
        components: {
            get: {
                type: "GET",
                endpoint: "/components"
            },
            getById: {
                type: "GET",
                endpoint: "/components/:id"
            },
            create: {
                type: "POST",
                endpoint: "/components"
            },
            update: {
                type: "PUT",
                endpoint: "/components/:id"
            },
            delete: {
                type: "DELETE",
                endpoint: "/components/:id"
            }
        },

        // Pages API endpoints
        pages: {
            get: {
                type: "GET",
                endpoint: "/pages"
            },
            getById: {
                type: "GET",
                endpoint: "/pages/:id"
            },
            create: {
                type: "POST",
                endpoint: "/pages"
            },
            update: {
                type: "PUT",
                endpoint: "/pages/:id"
            },
            delete: {
                type: "DELETE",
                endpoint: "/pages/:id"
            }
        },

        // Templates API endpoints
        templates: {
            get: {
                type: "GET",
                endpoint: "/templates"
            },
            getById: {
                type: "GET",
                endpoint: "/templates/:id"
            },
            create: {
                type: "POST",
                endpoint: "/templates"
            },
            update: {
                type: "PUT",
                endpoint: "/templates/:id"
            },
            delete: {
                type: "DELETE",
                endpoint: "/templates/:id"
            }
        },

        // Websites API endpoints
        websites: {
            get: {
                type: "GET",
                endpoint: "/websites"
            },
            getById: {
                type: "GET",
                endpoint: "/websites/:id"
            },
            create: {
                type: "POST",
                endpoint: "/websites"
            },
            update: {
                type: "PUT",
                endpoint: "/websites/:id"
            },
            delete: {
                type: "DELETE",
                endpoint: "/websites/:id"
            },
            export: {
                type: "POST",
                endpoint: "/export/website/:id"
            }
        },

        // Users API endpoints
        users: {
            get: {
                type: "GET",
                endpoint: "/auth/users"
            },
            create: {
                type: "POST",
                endpoint: "/auth/register"
            },
            update: {
                type: "PUT",
                endpoint: "/users/:id"
            },
            delete: {
                type: "DELETE",
                endpoint: "/users/:id"
            }
        },

        // Auth API endpoints
        auth: {
            login: {
                type: "POST",
                endpoint: "/auth/login"
            },
            register: {
                type: "POST",
                endpoint: "/auth/register"
            }
        },

        // File upload endpoint
        upload: {
            image: {
                type: "POST",
                endpoint: "/upload"
            }
        }
    }
};