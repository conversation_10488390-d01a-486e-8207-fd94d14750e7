import React from "react";
import { useSidebar } from "../../contexts/SidebarContext";
import Sidebar from "./Sidebar";
import Header from "./Header";
import { Outlet } from "react-router-dom";

const Layout = ({ title, subtitle, children }) => {
  const { isCollapsed, isMobile } = useSidebar();

  // Calculate margin based on sidebar state
  const getMainContentMargin = () => {
    if (isMobile) {
      return "tw-ml-0"; // No margin on mobile (sidebar is drawer)
    }
    return isCollapsed ? "tw-ml-20" : "tw-ml-64"; // Collapsed: 80px, Expanded: 256px
  };
  return (
    <div className=" tw-min-h-screen tw-bg-gray-50">
      <Sidebar />
      <div
        className={` tw-transition-all tw-duration-300 ${getMainContentMargin()}`}
      >
        <Header title={title} subtitle={subtitle} />
        <main className="">
          <Outlet />
        </main>
      </div>
    </div>
  );
};

export default Layout;
