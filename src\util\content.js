import { Globe, Component as Components, FileText, Layout, } from "lucide-react";

export const predefinedColors = [
    "#3B82F6",
    "#8B5CF6",
    "#10B981",
    "#F59E0B",
    "#EF4444",
    "#06B6D4",
    "#84CC16",
    "#F97316",
];

// Dashboard content ===============================================

export const statCards = [
    {
        name: "Total Websites",
        key: "websites",
        icon: Globe,
        color: "tw-from-blue-500 tw-to-blue-600",
        bgColor: "tw-bg-blue-50",
        iconColor: "tw-text-blue-600",
    },
    {
        name: "Components",
        icon: Components,
        key: "components",
        color: "tw-from-purple-500 tw-to-purple-600",
        bgColor: "tw-bg-purple-50",
        iconColor: "tw-text-purple-600",
    },
    {
        name: "Pages Created",
        icon: FileText,
        key: "pages",
        color: "tw-from-green-500 tw-to-green-600",
        bgColor: "tw-bg-green-50",
        iconColor: "tw-text-green-600",
    },
    {
        name: "Templates",
        icon: Layout,
        key: "templates",
        color: "tw-from-orange-500 tw-to-orange-600",
        bgColor: "tw-bg-orange-50",
        iconColor: "tw-text-orange-600",
    },
];

export const quickActions = [
    {
        name: "Create New Website",
        description: "Start building a new website from template",
        icon: Globe,
        color: "tw-from-blue-500 tw-to-blue-600",
        href: "/websites",
    },
    {
        name: "Build Component",
        description: "Create reusable components",
        icon: Components,
        color: "tw-from-purple-500 tw-to-purple-600",
        href: "/components",
    },
    {
        name: "Design Page",
        description: "Build pages with drag & drop",
        icon: FileText,
        color: "tw-from-green-500 tw-to-green-600",
        href: "/pages",
    },
    {
        name: "Manage Templates",
        description: "Organize page collections",
        icon: Layout,
        color: "tw-from-orange-500 tw-to-orange-600",
        href: "/templates",
    },
];

export const recentActivity = [
    {
        id: 1,
        label: "Create Categories",
        description: "Organize your components into categories",
        color: "blue",
        className: "tw-text-blue-600",

    },
    {
        id: 2,
        label: "Build Components",
        description: "Create reusable HTML components with placeholders",
        color: "purple",
        className: "tw-text-purple-600",
    },
    {
        id: 3,
        label: "Design Pages",
        description: "Drag and drop components to build pages",
        color: "green",
        className: "tw-text-green-600",
    },
    {
        id: 4,
        label: "Create Templates",
        description: "Group pages into reusable templates",
        color: "orange",
        className: "tw-text-orange-600",
    },
    {
        id: 5,
        label: "Generate Websites",
        description: "Create and export complete websites",
        color: "red",
        className: "tw-text-red-600",
    },
];

export const DND_TYPES = {
    LIB_ITEM: "LIB_ITEM",
    STRUCT_ITEM: "STRUCT_ITEM",
};