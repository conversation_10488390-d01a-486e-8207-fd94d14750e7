import pkg from "sqlite3";
import path from "path";
import bcrypt from "bcryptjs";
import { fileURLToPath } from "url";
import { dirname } from "path";

const { Database } = pkg.verbose();

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const dbPath = path.join(__dirname, "database.sqlite");
const db = new Database(dbPath);

const initializeDatabase = () => {
  return new Promise((resolve, reject) => {
    db.serialize(() => {
      // Users table
      db.run(`
        CREATE TABLE IF NOT EXISTS users (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          username TEXT UNIQUE NOT NULL,
          email TEXT UNIQUE NOT NULL,
          password TEXT NOT NULL,
          role TEXT DEFAULT 'content_team',
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // Categories table
      db.run(`
        CREATE TABLE IF NOT EXISTS categories (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          description TEXT,
          color TEXT DEFAULT '#3B82F6',
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // Components table
      db.run(`
        CREATE TABLE IF NOT EXISTS components (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          category_id INTEGER,
          html_content TEXT NOT NULL,
          css_content TEXT,
          js_content TEXT,
          placeholders TEXT,
          preview_image TEXT,
          version INTEGER DEFAULT 1,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (category_id) REFERENCES categories (id)
        )
      `);

      // Pages table
      db.run(`
        CREATE TABLE IF NOT EXISTS pages (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          slug TEXT NOT NULL,
          components TEXT,
          meta_title TEXT,
          meta_description TEXT,
          custom_css TEXT,
          custom_js TEXT,
          version INTEGER DEFAULT 1,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // Templates table
      db.run(`
        CREATE TABLE IF NOT EXISTS templates (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          description TEXT,
          pages TEXT,
          version INTEGER DEFAULT 1,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // Websites table
      db.run(`
        CREATE TABLE IF NOT EXISTS websites (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          template_id INTEGER,
          primary_color TEXT DEFAULT '#3B82F6',
          secondary_color TEXT DEFAULT '#8B5CF6',
          logo_url TEXT,
          content_data TEXT,
          status TEXT DEFAULT 'draft',
          version INTEGER DEFAULT 1,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (template_id) REFERENCES templates (id)
        )
      `);

      // Activity logs table
      db.run(`
        CREATE TABLE IF NOT EXISTS activity_logs (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          user_id INTEGER,
          action TEXT NOT NULL,
          entity_type TEXT NOT NULL,
          entity_id INTEGER,
          details TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (user_id) REFERENCES users (id)
        )
      `);

      // Insert default admin user
      const adminPassword = bcrypt.hashSync("admin123", 10);
      db.run(
        `
        INSERT OR IGNORE INTO users (username, email, password, role)
        VALUES ('admin', '<EMAIL>', ?, 'admin')
      `,
        [adminPassword]
      );

      // Insert default categories
      const defaultCategories = [
        {
          name: "Headers",
          description: "Navigation and header components",
          color: "#3B82F6",
        },
        {
          name: "Content",
          description: "Text and content blocks",
          color: "#10B981",
        },
        {
          name: "Media",
          description: "Image and video components",
          color: "#F59E0B",
        },
        {
          name: "Forms",
          description: "Input and form components",
          color: "#EF4444",
        },
        {
          name: "Footers",
          description: "Footer and bottom section components",
          color: "#8B5CF6",
        },
      ];

      defaultCategories.forEach((category) => {
        db.run(
          `
          INSERT OR IGNORE INTO categories (name, description, color)
          VALUES (?, ?, ?)
        `,
          [category.name, category.description, category.color]
        );
      });

      // Insert sample components
      const sampleComponents = [
        {
          name: "Hero Section",
          category_id: 1,
          html_content: `<section class="tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 tw-text-white tw-py-20">
            <div class="tw-container tw-mx-auto tw-px-4 tw-text-center">
              <h1 class="tw-text-5xl tw-font-bold tw-mb-6">\${hero_title}</h1>
              <p class="tw-text-xl tw-mb-8">\${hero_subtitle}</p>
              <button class="tw-bg-white tw-text-blue-600 tw-px-8 tw-py-3 tw-rounded-lg tw-font-semibold tw-hover:tw-bg-gray-100 tw-transition-colors">\${cta_text}</button>
            </div>
          </section>`,
          css_content: "",
          js_content: "",
          placeholders: JSON.stringify([
            "hero_title",
            "hero_subtitle",
            "cta_text",
          ]),
        },
        {
          name: "Navigation Bar",
          category_id: 1,
          html_content: `<nav class="tw-bg-white tw-shadow-lg">
            <div class="tw-container tw-mx-auto tw-px-4">
              <div class="tw-flex tw-justify-between tw-items-center tw-py-4">
                <div class="tw-flex tw-items-center">
                  <img src="\${logo_url}" alt="\${site_name}" class="tw-h-8 tw-w-auto">
                  <span class="tw-ml-2 tw-text-xl tw-font-bold tw-text-gray-800">\${site_name}</span>
                </div>
                <div class="tw-hidden tw-md:block">
                  <div class="tw-flex tw-space-x-8">
                    <a href="#" class="tw-text-gray-600 tw-hover:tw-text-blue-600 tw-transition-colors">\${nav_item_1}</a>
                    <a href="#" class="tw-text-gray-600 tw-hover:tw-text-blue-600 tw-transition-colors">\${nav_item_2}</a>
                    <a href="#" class="tw-text-gray-600 tw-hover:tw-text-blue-600 tw-transition-colors">\${nav_item_3}</a>
                  </div>
                </div>
              </div>
            </div>
          </nav>`,
          css_content: "",
          js_content: "",
          placeholders: JSON.stringify([
            "logo_url",
            "site_name",
            "nav_item_1",
            "nav_item_2",
            "nav_item_3",
          ]),
        },
        {
          name: "Content Block",
          category_id: 2,
          html_content: `<section class="tw-py-16 tw-bg-gray-50">
            <div class="tw-container tw-mx-auto tw-px-4">
              <div class="tw-max-w-3xl tw-mx-auto tw-text-center">
                <h2 class="tw-text-3xl tw-font-bold tw-text-gray-900 tw-mb-6">\${section_title}</h2>
                <p class="tw-text-lg tw-text-gray-600 tw-mb-8">\${section_content}</p>
                <div class="tw-grid tw-md:tw-grid-cols-3 tw-gap-8">
                  <div class="tw-bg-white tw-p-6 tw-rounded-lg tw-shadow-md">
                    <h3 class="tw-text-xl tw-font-semibold tw-mb-4">\${feature_1_title}</h3>
                    <p class="tw-text-gray-600">\${feature_1_description}</p>
                  </div>
                  <div class="tw-bg-white tw-p-6 tw-rounded-lg tw-shadow-md">
                    <h3 class="tw-text-xl tw-font-semibold tw-mb-4">\${feature_2_title}</h3>
                    <p class="tw-text-gray-600">\${feature_2_description}</p>
                  </div>
                  <div class="tw-bg-white tw-p-6 tw-rounded-lg tw-shadow-md">
                    <h3 class="tw-text-xl tw-font-semibold tw-mb-4">\${feature_3_title}</h3>
                    <p class="tw-text-gray-600">\${feature_3_description}</p>
                  </div>
                </div>
              </div>
            </div>
          </section>`,
          css_content: "",
          js_content: "",
          placeholders: JSON.stringify([
            "section_title",
            "section_content",
            "feature_1_title",
            "feature_1_description",
            "feature_2_title",
            "feature_2_description",
            "feature_3_title",
            "feature_3_description",
          ]),
        },
      ];

      sampleComponents.forEach((component, index) => {
        setTimeout(() => {
          db.run(
            `
            INSERT OR IGNORE INTO components (name, category_id, html_content, css_content, js_content, placeholders)
            VALUES (?, ?, ?, ?, ?, ?)
          `,
            [
              component.name,
              component.category_id,
              component.html_content,
              component.css_content,
              component.js_content,
              component.placeholders,
            ]
          );
        }, index * 100);
      });

      console.log("Database initialized successfully");
      console.log("Sample data created: Categories, Components");
      resolve();
    });
  });
};

export { db, initializeDatabase };
