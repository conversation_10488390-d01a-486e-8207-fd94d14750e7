/**
 * JSON Storage Utility for Local File Management
 * Provides CRUD operations for storing data in JSON files
 * Used as temporary solution while backend is not ready
 */

// Storage keys for different data types
export const STORAGE_KEYS = {
  CATEGORIES: 'categories',
  COMPONENTS: 'components',
  PAGES: 'pages',
  TEMPLATES: 'templates',
  WEBSITES: 'websites',
  USERS: 'users'
};

// Default data structures with new fields
const DEFAULT_DATA = {
  [STORAGE_KEYS.CATEGORIES]: [],
  [STORAGE_KEYS.COMPONENTS]: [],
  [STORAGE_KEYS.PAGES]: [],
  [STORAGE_KEYS.TEMPLATES]: [],
  [STORAGE_KEYS.WEBSITES]: [],
  [STORAGE_KEYS.USERS]: []
};

// Sample data for initialization
const SAMPLE_DATA = {
  [STORAGE_KEYS.CATEGORIES]: [
    {
      id: 'cat1',
      name: 'Headers',
      description: 'Navigation and header components',
      color: '#3B82F6',
      slug: 'headers',
      sort_order: 1,
      created_at: '2024-01-01T00:00:00.000Z',
      updated_at: '2024-01-01T00:00:00.000Z'
    },
    {
      id: 'cat2',
      name: 'Content',
      description: 'Text and content blocks',
      color: '#10B981',
      slug: 'content',
      sort_order: 2,
      created_at: '2024-01-01T00:00:00.000Z',
      updated_at: '2024-01-01T00:00:00.000Z'
    },
    {
      id: 'cat3',
      name: 'Forms',
      description: 'Input and form components',
      color: '#EF4444',
      slug: 'forms',
      sort_order: 3,
      created_at: '2024-01-01T00:00:00.000Z',
      updated_at: '2024-01-01T00:00:00.000Z'
    }
  ],
  [STORAGE_KEYS.COMPONENTS]: [
    {
      id: 'comp1',
      name: 'Hero Section',
      category_id: 'cat1',
      html_content: '<section class="tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 tw-text-white tw-py-20"><div class="tw-container tw-mx-auto tw-px-4 tw-text-center"><h1 class="tw-text-5xl tw-font-bold tw-mb-6">${hero_title}</h1><p class="tw-text-xl tw-mb-8">${hero_subtitle}</p><button class="tw-bg-white tw-text-blue-600 tw-px-8 tw-py-3 tw-rounded-lg tw-font-semibold tw-hover:tw-bg-gray-100 tw-transition-colors">${cta_text}</button></div></section>',
      css_content: '',
      js_content: '',
      placeholders: ['hero_title', 'hero_subtitle', 'cta_text'],
      preview_image: '',
      version: 1,
      status: 'published',
      tags: ['hero', 'header', 'gradient'],
      thumbnail_url: '',
      created_at: '2024-01-01T00:00:00.000Z',
      updated_at: '2024-01-01T00:00:00.000Z'
    },
    {
      id: 'comp2',
      name: 'Contact Form',
      category_id: 'cat3',
      html_content: '<form class="tw-max-w-md tw-mx-auto tw-bg-white tw-p-8 tw-rounded-lg tw-shadow-lg"><h2 class="tw-text-2xl tw-font-bold tw-mb-6 tw-text-center">${form_title}</h2><div class="tw-mb-4"><input type="text" placeholder="${name_placeholder}" class="tw-w-full tw-px-4 tw-py-2 tw-border tw-rounded-lg tw-focus:outline-none tw-focus:ring-2 tw-focus:ring-blue-500"></div><div class="tw-mb-4"><input type="email" placeholder="${email_placeholder}" class="tw-w-full tw-px-4 tw-py-2 tw-border tw-rounded-lg tw-focus:outline-none tw-focus:ring-2 tw-focus:ring-blue-500"></div><div class="tw-mb-6"><textarea placeholder="${message_placeholder}" rows="4" class="tw-w-full tw-px-4 tw-py-2 tw-border tw-rounded-lg tw-focus:outline-none tw-focus:ring-2 tw-focus:ring-blue-500"></textarea></div><button type="submit" class="tw-w-full tw-bg-blue-600 tw-text-white tw-py-2 tw-rounded-lg tw-hover:tw-bg-blue-700 tw-transition-colors">${submit_text}</button></form>',
      css_content: '',
      js_content: '',
      placeholders: ['form_title', 'name_placeholder', 'email_placeholder', 'message_placeholder', 'submit_text'],
      preview_image: '',
      version: 1,
      status: 'draft',
      tags: ['form', 'contact', 'input'],
      thumbnail_url: '',
      created_at: '2024-01-01T00:00:00.000Z',
      updated_at: '2024-01-01T00:00:00.000Z'
    }
  ],
  [STORAGE_KEYS.PAGES]: [
    {
      id: 'page1',
      name: 'Home Page',
      slug: 'home',
      meta_title: 'Welcome to Our Website',
      meta_description: 'This is the home page of our amazing website',
      wrapper_class: 'home-wrapper',
      custom_css: 'body { background-color: #f8f9fa; }',
      custom_js: 'console.log("Home page loaded");',
      full_page_content: '<div class="home-page">Full page content here</div>',
      pageComponentList: [
        {
          version: 'v1',
          name: 'Hero Section',
          class: 'hero-component',
          repeator: 'single',
          position: 0
        }
      ],
      components: [],
      version: 1,
      created_at: '2024-01-01T00:00:00.000Z',
      updated_at: '2024-01-01T00:00:00.000Z'
    },
    {
      id: 'page2',
      name: 'About Us',
      slug: 'about',
      meta_title: 'About Our Company',
      meta_description: 'Learn more about our company and mission',
      wrapper_class: 'about-wrapper',
      custom_css: '',
      custom_js: '',
      full_page_content: '<div class="about-page">About page content</div>',
      pageComponentList: [
        {
          version: 'v1',
          name: 'Content Section',
          class: 'content-component',
          repeator: 'single',
          position: 0
        }
      ],
      components: [],
      version: 1,
      created_at: '2024-01-01T00:00:00.000Z',
      updated_at: '2024-01-01T00:00:00.000Z'
    }
  ],
  [STORAGE_KEYS.TEMPLATES]: [
    {
      id: 'template1',
      name: 'Business Website',
      description: 'A professional business website template',
      full_template_content: '<div class="business-template">Full template content here</div>',
      templateComponentList: [
        {
          version: 'v1',
          url: '/home',
          repeator: 'single',
          position: 0,
          showNavbar: true,
          navPosition: 0
        },
        {
          version: 'v1',
          url: '/about',
          repeator: 'single',
          position: 1,
          showNavbar: true,
          navPosition: 1
        }
      ],
      pages: ['page1', 'page2'],
      content: [
        {
          type: 'hero',
          data: { title: 'Welcome to Business', subtitle: 'Professional solutions' }
        },
        {
          type: 'features',
          data: { items: ['Feature 1', 'Feature 2', 'Feature 3'] }
        }
      ],
      version: 1,
      created_at: '2024-01-01T00:00:00.000Z',
      updated_at: '2024-01-01T00:00:00.000Z'
    }
  ]
};

// Initialize storage with default data if not exists
const initializeStorage = () => {
  Object.keys(DEFAULT_DATA).forEach(key => {
    if (!localStorage.getItem(key)) {
      // Use sample data for categories and components, empty arrays for others
      const initialData = SAMPLE_DATA[key] || DEFAULT_DATA[key];
      localStorage.setItem(key, JSON.stringify(initialData));
    }
  });
};

// Auto-slugify function for generating slugs from names
export const generateSlug = (name) => {
  if (!name) return '';
  return name
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
};

// Generate unique ID
export const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

// Get current timestamp
export const getCurrentTimestamp = () => {
  return new Date().toISOString();
};

/**
 * Core JSON Storage Class
 */
class JSONStorage {
  constructor() {
    initializeStorage();
  }

  // Get all data for a specific type
  getAll(type) {
    try {
      const data = localStorage.getItem(type);
      return data ? JSON.parse(data) : DEFAULT_DATA[type] || [];
    } catch (error) {
      console.error(`Error getting ${type} data:`, error);
      return DEFAULT_DATA[type] || [];
    }
  }

  // Get single item by ID
  getById(type, id) {
    try {
      const data = this.getAll(type);
      return data.find(item => item.id === id) || null;
    } catch (error) {
      console.error(`Error getting ${type} by ID:`, error);
      return null;
    }
  }

  // Save all data for a specific type
  saveAll(type, data) {
    try {
      localStorage.setItem(type, JSON.stringify(data));
      return true;
    } catch (error) {
      console.error(`Error saving ${type} data:`, error);
      return false;
    }
  }

  // Create new item
  create(type, itemData) {
    try {
      const data = this.getAll(type);
      const newItem = {
        ...itemData,
        id: generateId(),
        created_at: getCurrentTimestamp(),
        updated_at: getCurrentTimestamp()
      };

      // Auto-generate slug for categories if not provided
      if (type === STORAGE_KEYS.CATEGORIES && !newItem.slug && newItem.name) {
        newItem.slug = generateSlug(newItem.name);
      }

      data.push(newItem);
      this.saveAll(type, data);
      return newItem;
    } catch (error) {
      console.error(`Error creating ${type}:`, error);
      return null;
    }
  }

  // Update existing item
  update(type, id, updateData) {
    try {
      const data = this.getAll(type);
      const index = data.findIndex(item => item.id === id);

      if (index === -1) {
        console.error(`${type} with ID ${id} not found`);
        return null;
      }

      const updatedItem = {
        ...data[index],
        ...updateData,
        updated_at: getCurrentTimestamp()
      };

      // Auto-update slug for categories if name changed
      if (type === STORAGE_KEYS.CATEGORIES && updateData.name && !updateData.slug) {
        updatedItem.slug = generateSlug(updateData.name);
      }

      data[index] = updatedItem;
      this.saveAll(type, data);
      return updatedItem;
    } catch (error) {
      console.error(`Error updating ${type}:`, error);
      return null;
    }
  }

  // Delete item
  delete(type, id) {
    try {
      const data = this.getAll(type);
      const filteredData = data.filter(item => item.id !== id);

      if (data.length === filteredData.length) {
        console.error(`${type} with ID ${id} not found`);
        return false;
      }

      this.saveAll(type, filteredData);
      return true;
    } catch (error) {
      console.error(`Error deleting ${type}:`, error);
      return false;
    }
  }

  // Clear all data for a type (useful for testing)
  clear(type) {
    try {
      this.saveAll(type, DEFAULT_DATA[type] || []);
      return true;
    } catch (error) {
      console.error(`Error clearing ${type}:`, error);
      return false;
    }
  }

  // Clear all storage
  clearAll() {
    try {
      Object.keys(DEFAULT_DATA).forEach(key => {
        this.clear(key);
      });
      return true;
    } catch (error) {
      console.error('Error clearing all storage:', error);
      return false;
    }
  }

  // Export data (for backup)
  exportData() {
    try {
      const allData = {};
      Object.keys(DEFAULT_DATA).forEach(key => {
        allData[key] = this.getAll(key);
      });
      return allData;
    } catch (error) {
      console.error('Error exporting data:', error);
      return null;
    }
  }

  // Import data (for restore)
  importData(data) {
    try {
      Object.keys(data).forEach(key => {
        if (DEFAULT_DATA.hasOwnProperty(key)) {
          this.saveAll(key, data[key]);
        }
      });
      return true;
    } catch (error) {
      console.error('Error importing data:', error);
      return false;
    }
  }
}

// Create singleton instance
const jsonStorage = new JSONStorage();

export default jsonStorage;
