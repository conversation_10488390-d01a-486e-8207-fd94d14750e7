import express from "express";
import { db } from "../database.js";
import { authenticateToken } from "./auth.js";

const router = express.Router();

// Get all pages
router.get("/", authenticateToken, (req, res) => {
  db.all("SELECT * FROM pages ORDER BY name", (err, pages) => {
    if (err) {
      return res.status(500).json({ error: "Database error" });
    }

    const pagesWithParsedData = pages.map((page) => ({
      ...page,
      components: page.components ? JSON.parse(page.components) : [],
    }));

    res.json(pagesWithParsedData);
  });
});

// Get page by ID
router.get("/:id", authenticateToken, (req, res) => {
  const { id } = req.params;

  db.get("SELECT * FROM pages WHERE id = ?", [id], (err, page) => {
    if (err) {
      return res.status(500).json({ error: "Database error" });
    }
    if (!page) {
      return res.status(404).json({ error: "Page not found" });
    }

    page.components = page.components ? JSON.parse(page.components) : [];
    res.json(page);
  });
});

// Create page
router.post("/", authenticateToken, (req, res) => {
  const {
    name,
    slug,
    components,
    meta_title,
    meta_description,
    custom_css,
    custom_js,
  } = req.body;

  db.run(
    "INSERT INTO pages (name, slug, components, meta_title, meta_description, custom_css, custom_js) VALUES (?, ?, ?, ?, ?, ?, ?)",
    [
      name,
      slug,
      JSON.stringify(components || []),
      meta_title,
      meta_description,
      custom_css || "",
      custom_js || "",
    ],
    function (err) {
      if (err) {
        return res.status(500).json({ error: "Failed to create page" });
      }

      db.get("SELECT * FROM pages WHERE id = ?", [this.lastID], (err, page) => {
        if (err) {
          return res.status(500).json({ error: "Database error" });
        }
        page.components = page.components ? JSON.parse(page.components) : [];
        res.json(page);
      });
    }
  );
});

// Update page
router.put("/:id", authenticateToken, (req, res) => {
  const { id } = req.params;
  const {
    name,
    slug,
    components,
    meta_title,
    meta_description,
    custom_css,
    custom_js,
  } = req.body;

  db.run(
    "UPDATE pages SET name = ?, slug = ?, components = ?, meta_title = ?, meta_description = ?, custom_css = ?, custom_js = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?",
    [
      name,
      slug,
      JSON.stringify(components || []),
      meta_title,
      meta_description,
      custom_css || "",
      custom_js || "",
      id,
    ],
    function (err) {
      if (err) {
        return res.status(500).json({ error: "Failed to update page" });
      }

      db.get("SELECT * FROM pages WHERE id = ?", [id], (err, page) => {
        if (err) {
          return res.status(500).json({ error: "Database error" });
        }
        page.components = page.components ? JSON.parse(page.components) : [];
        res.json(page);
      });
    }
  );
});

// Delete page
router.delete("/:id", authenticateToken, (req, res) => {
  const { id } = req.params;

  db.run("DELETE FROM pages WHERE id = ?", [id], function (err) {
    if (err) {
      return res.status(500).json({ error: "Failed to delete page" });
    }
    res.json({ message: "Page deleted successfully" });
  });
});

export default router;
