import express from "express";
import { db } from "../database.js";
import { authenticateToken } from "./auth.js";

const router = express.Router();

// Get all categories
router.get("/", authenticateToken, (req, res) => {
  db.all("SELECT * FROM categories ORDER BY name", (err, categories) => {
    if (err) {
      return res.status(500).json({ error: "Database error" });
    }
    res.json(categories);
  });
});

// Create category (Admin only)
router.post("/", authenticateToken, (req, res) => {
  if (req.user.role !== "admin") {
    return res.status(403).json({ error: "Admin access required" });
  }

  const { name, description, color } = req.body;

  db.run(
    "INSERT INTO categories (name, description, color) VALUES (?, ?, ?)",
    [name, description, color || "#3B82F6"],
    function (err) {
      if (err) {
        return res.status(500).json({ error: "Failed to create category" });
      }

      db.get(
        "SELECT * FROM categories WHERE id = ?",
        [this.lastID],
        (err, category) => {
          if (err) {
            return res.status(500).json({ error: "Database error" });
          }
          res.json(category);
        }
      );
    }
  );
});

// Update category (Admin only)
router.put("/:id", authenticateToken, (req, res) => {
  if (req.user.role !== "admin") {
    return res.status(403).json({ error: "Admin access required" });
  }

  const { id } = req.params;
  const { name, description, color } = req.body;

  db.run(
    "UPDATE categories SET name = ?, description = ?, color = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?",
    [name, description, color, id],
    function (err) {
      if (err) {
        return res.status(500).json({ error: "Failed to update category" });
      }

      db.get("SELECT * FROM categories WHERE id = ?", [id], (err, category) => {
        if (err) {
          return res.status(500).json({ error: "Database error" });
        }
        res.json(category);
      });
    }
  );
});

// Delete category (Admin only)
router.delete("/:id", authenticateToken, (req, res) => {
  if (req.user.role !== "admin") {
    return res.status(403).json({ error: "Admin access required" });
  }

  const { id } = req.params;

  db.run("DELETE FROM categories WHERE id = ?", [id], function (err) {
    if (err) {
      return res.status(500).json({ error: "Failed to delete category" });
    }
    res.json({ message: "Category deleted successfully" });
  });
});

export default router;
