import express from "express";
import { db } from "../database.js";
import { authenticateToken } from "./auth.js";

const router = express.Router();

// Get all components
router.get("/", authenticateToken, (req, res) => {
  const query = `
    SELECT c.*, cat.name as category_name, cat.color as category_color
    FROM components c
    LEFT JOIN categories cat ON c.category_id = cat.id
    ORDER BY c.name
  `;

  db.all(query, (err, components) => {
    if (err) {
      return res.status(500).json({ error: "Database error" });
    }

    // Parse placeholders JSON
    const componentsWithParsedData = components.map((component) => ({
      ...component,
      placeholders: component.placeholders
        ? JSON.parse(component.placeholders)
        : [],
    }));

    res.json(componentsWithParsedData);
  });
});

// Get component by ID
router.get("/:id", authenticateToken, (req, res) => {
  const { id } = req.params;

  const query = `
    SELECT c.*, cat.name as category_name, cat.color as category_color
    FROM components c
    LEFT JOIN categories cat ON c.category_id = cat.id
    WHERE c.id = ?
  `;

  db.get(query, [id], (err, component) => {
    if (err) {
      return res.status(500).json({ error: "Database error" });
    }
    if (!component) {
      return res.status(404).json({ error: "Component not found" });
    }

    component.placeholders = component.placeholders
      ? JSON.parse(component.placeholders)
      : [];
    res.json(component);
  });
});

// Create component (Admin only)
router.post("/", authenticateToken, (req, res) => {
  if (req.user.role !== "admin") {
    return res.status(403).json({ error: "Admin access required" });
  }

  const {
    name,
    category_id,
    html_content,
    css_content,
    js_content,
    placeholders,
  } = req.body;

  db.run(
    "INSERT INTO components (name, category_id, html_content, css_content, js_content, placeholders) VALUES (?, ?, ?, ?, ?, ?)",
    [
      name,
      category_id,
      html_content,
      css_content || "",
      js_content || "",
      JSON.stringify(placeholders || []),
    ],
    function (err) {
      if (err) {
        return res.status(500).json({ error: "Failed to create component" });
      }

      db.get(
        "SELECT * FROM components WHERE id = ?",
        [this.lastID],
        (err, component) => {
          if (err) {
            return res.status(500).json({ error: "Database error" });
          }
          component.placeholders = component.placeholders
            ? JSON.parse(component.placeholders)
            : [];
          res.json(component);
        }
      );
    }
  );
});

// Update component (Admin only)
router.put("/:id", authenticateToken, (req, res) => {
  if (req.user.role !== "admin") {
    return res.status(403).json({ error: "Admin access required" });
  }

  const { id } = req.params;
  const {
    name,
    category_id,
    html_content,
    css_content,
    js_content,
    placeholders,
  } = req.body;

  db.run(
    "UPDATE components SET name = ?, category_id = ?, html_content = ?, css_content = ?, js_content = ?, placeholders = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?",
    [
      name,
      category_id,
      html_content,
      css_content || "",
      js_content || "",
      JSON.stringify(placeholders || []),
      id,
    ],
    function (err) {
      if (err) {
        return res.status(500).json({ error: "Failed to update component" });
      }

      db.get(
        "SELECT * FROM components WHERE id = ?",
        [id],
        (err, component) => {
          if (err) {
            return res.status(500).json({ error: "Database error" });
          }
          component.placeholders = component.placeholders
            ? JSON.parse(component.placeholders)
            : [];
          res.json(component);
        }
      );
    }
  );
});

// Delete component (Admin only)
router.delete("/:id", authenticateToken, (req, res) => {
  if (req.user.role !== "admin") {
    return res.status(403).json({ error: "Admin access required" });
  }

  const { id } = req.params;

  db.run("DELETE FROM components WHERE id = ?", [id], function (err) {
    if (err) {
      return res.status(500).json({ error: "Failed to delete component" });
    }
    res.json({ message: "Component deleted successfully" });
  });
});

export default router;
