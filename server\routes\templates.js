import express from "express";
import { db } from "../database.js";
import { authenticateToken } from "./auth.js";

const router = express.Router();

// Get all templates
router.get("/", authenticateToken, (req, res) => {
  db.all("SELECT * FROM templates ORDER BY name", (err, templates) => {
    if (err) {
      return res.status(500).json({ error: "Database error" });
    }

    const templatesWithParsedData = templates.map((template) => ({
      ...template,
      pages: template.pages ? JSON.parse(template.pages) : [],
    }));

    res.json(templatesWithParsedData);
  });
});

// Get template by ID
router.get("/:id", authenticateToken, (req, res) => {
  const { id } = req.params;

  db.get("SELECT * FROM templates WHERE id = ?", [id], (err, template) => {
    if (err) {
      return res.status(500).json({ error: "Database error" });
    }
    if (!template) {
      return res.status(404).json({ error: "Template not found" });
    }

    template.pages = template.pages ? JSON.parse(template.pages) : [];
    res.json(template);
  });
});

// Create template
router.post("/", authenticateToken, (req, res) => {
  const { name, description, pages } = req.body;

  db.run(
    "INSERT INTO templates (name, description, pages) VALUES (?, ?, ?)",
    [name, description, JSON.stringify(pages || [])],
    function (err) {
      if (err) {
        return res.status(500).json({ error: "Failed to create template" });
      }

      db.get(
        "SELECT * FROM templates WHERE id = ?",
        [this.lastID],
        (err, template) => {
          if (err) {
            return res.status(500).json({ error: "Database error" });
          }
          template.pages = template.pages ? JSON.parse(template.pages) : [];
          res.json(template);
        }
      );
    }
  );
});

// Update template
router.put("/:id", authenticateToken, (req, res) => {
  const { id } = req.params;
  const { name, description, pages } = req.body;

  db.run(
    "UPDATE templates SET name = ?, description = ?, pages = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?",
    [name, description, JSON.stringify(pages || []), id],
    function (err) {
      if (err) {
        return res.status(500).json({ error: "Failed to update template" });
      }

      db.get("SELECT * FROM templates WHERE id = ?", [id], (err, template) => {
        if (err) {
          return res.status(500).json({ error: "Database error" });
        }
        template.pages = template.pages ? JSON.parse(template.pages) : [];
        res.json(template);
      });
    }
  );
});

// Delete template
router.delete("/:id", authenticateToken, (req, res) => {
  const { id } = req.params;

  db.run("DELETE FROM templates WHERE id = ?", [id], function (err) {
    if (err) {
      return res.status(500).json({ error: "Failed to delete template" });
    }
    res.json({ message: "Template deleted successfully" });
  });
});

export default router;
