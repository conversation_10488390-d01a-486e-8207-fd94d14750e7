import React from "react";
import { Activity, Clock, User, FileText } from "lucide-react";
import { Card, Timeline, Tag, Avatar } from "antd";

const ActivityLogs = () => {
  // Mock data for activity logs
  const activities = [
    {
      id: 1,
      type: "component_created",
      user: "<PERSON> Doe",
      action: "Created new component",
      target: "Hero Section",
      timestamp: "2 minutes ago",
      icon: <FileText className="tw-w-4 tw-h-4" />,
      color: "green",
    },
    {
      id: 2,
      type: "component_updated",
      user: "<PERSON>",
      action: "Updated component",
      target: "Navigation Bar",
      timestamp: "15 minutes ago",
      icon: <FileText className="tw-w-4 tw-h-4" />,
      color: "blue",
    },
    {
      id: 3,
      type: "user_login",
      user: "Admin",
      action: "User logged in",
      target: "System",
      timestamp: "1 hour ago",
      icon: <User className="tw-w-4 tw-h-4" />,
      color: "purple",
    },
    {
      id: 4,
      type: "component_deleted",
      user: "<PERSON>",
      action: "Deleted component",
      target: "Old Footer",
      timestamp: "2 hours ago",
      icon: <FileText className="tw-w-4 tw-h-4" />,
      color: "red",
    },
  ];

  const timelineItems = activities.map((activity) => ({
    dot: (
      <div className={`tw-w-8 tw-h-8 tw-rounded-full tw-flex tw-items-center tw-justify-center tw-bg-${activity.color}-100`}>
        {activity.icon}
      </div>
    ),
    children: (
      <Card className="tw-mb-4 tw-shadow-sm">
        <div className="tw-flex tw-items-start tw-justify-between">
          <div className="tw-flex-1">
            <div className="tw-flex tw-items-center tw-gap-2 tw-mb-2">
              <Avatar size="small" className="!tw-bg-gray-500">
                {activity.user.charAt(0)}
              </Avatar>
              <span className="tw-font-medium tw-text-gray-900">
                {activity.user}
              </span>
              <Tag color={activity.color}>{activity.type.replace('_', ' ')}</Tag>
            </div>
            <p className="tw-text-gray-700 tw-mb-1">
              {activity.action}: <span className="tw-font-medium">{activity.target}</span>
            </p>
            <div className="tw-flex tw-items-center tw-text-sm tw-text-gray-500">
              <Clock className="tw-w-3 tw-h-3 tw-mr-1" />
              {activity.timestamp}
            </div>
          </div>
        </div>
      </Card>
    ),
  }));

  return (
    <div className="tw-p-6">
      <div className="tw-mb-6">
        <div className="tw-flex tw-items-center tw-gap-3 tw-mb-2">
          <Activity className="tw-w-6 tw-h-6 tw-text-blue-600" />
          <h1 className="tw-text-2xl tw-font-bold tw-text-gray-900">
            Activity Logs
          </h1>
        </div>
        <p className="tw-text-gray-600">
          Track all system activities and user actions
        </p>
      </div>

      <Card className="tw-shadow-sm">
        <Timeline items={timelineItems} />
      </Card>
    </div>
  );
};

export default ActivityLogs;
