/**
 * CRUD Operations for JSON Storage
 * Provides specific CRUD operations for each data type with validation and business logic
 */

import jsonStorage, { STORAGE_KEYS, generateSlug } from './jsonStorage.js';

// Default field values for new items
const DEFAULT_VALUES = {
  categories: {
    name: '',
    description: '',
    color: '#3B82F6',
    slug: '',
    sort_order: 0
  },
  components: {
    name: '',
    category_id: null,
    html_content: '',
    css_content: '',
    js_content: '',
    placeholders: [],
    preview_image: '',
    version: 1,
    status: 'draft', // New field: draft | published
    tags: [], // New field: string array
    thumbnail_url: '' // New field: optional image URL
  },
  pages: {
    name: '',
    slug: '',
    meta_title: '',
    meta_description: '',
    wrapper_class: '', // New field
    custom_css: '',
    custom_js: '',
    full_page_content: '', // New field: component preview full code
    pageComponentList: [], // New field: page preview component list
    components: [], // Existing field for backward compatibility
    version: 1
  },
  templates: {
    name: '', // Template name
    description: '',
    full_template_content: '', // New field: template preview full code
    templateComponentList: [], // New field: template preview page list
    pages: [], // Existing field for backward compatibility
    content: [], // New field: array of json
    version: 1
  }
};

/**
 * Categories CRUD Operations
 */
export const categoriesCRUD = {
  // Get all categories
  getAll: () => {
    return jsonStorage.getAll(STORAGE_KEYS.CATEGORIES);
  },

  // Get category by ID
  getById: (id) => {
    return jsonStorage.getById(STORAGE_KEYS.CATEGORIES, id);
  },

  // Create new category
  create: (categoryData) => {
    const newCategory = {
      ...DEFAULT_VALUES.categories,
      ...categoryData
    };

    // Auto-generate slug if not provided
    if (!newCategory.slug && newCategory.name) {
      newCategory.slug = generateSlug(newCategory.name);
    }

    // Validate required fields
    if (!newCategory.name) {
      throw new Error('Category name is required');
    }

    return jsonStorage.create(STORAGE_KEYS.CATEGORIES, newCategory);
  },

  // Update category
  update: (id, updateData) => {
    // Auto-generate slug if name is updated but slug is not provided
    if (updateData.name && !updateData.slug) {
      updateData.slug = generateSlug(updateData.name);
    }

    return jsonStorage.update(STORAGE_KEYS.CATEGORIES, id, updateData);
  },

  // Delete category
  delete: (id) => {
    return jsonStorage.delete(STORAGE_KEYS.CATEGORIES, id);
  }
};

/**
 * Components CRUD Operations
 */
export const componentsCRUD = {
  // Get all components
  getAll: () => {
    const components = jsonStorage.getAll(STORAGE_KEYS.COMPONENTS);
    const categories = jsonStorage.getAll(STORAGE_KEYS.CATEGORIES);
    
    // Enrich components with category information
    return components.map(component => {
      const category = categories.find(cat => cat.id === component.category_id);
      return {
        ...component,
        category_name: category?.name || '',
        category_color: category?.color || '#3B82F6'
      };
    });
  },

  // Get component by ID
  getById: (id) => {
    return jsonStorage.getById(STORAGE_KEYS.COMPONENTS, id);
  },

  // Create new component
  create: (componentData) => {
    const newComponent = {
      ...DEFAULT_VALUES.components,
      ...componentData
    };

    // Validate required fields
    if (!newComponent.name) {
      throw new Error('Component name is required');
    }
    if (!newComponent.html_content) {
      throw new Error('Component HTML content is required');
    }

    // Ensure placeholders is an array
    if (typeof newComponent.placeholders === 'string') {
      try {
        newComponent.placeholders = JSON.parse(newComponent.placeholders);
      } catch (e) {
        newComponent.placeholders = [];
      }
    }

    // Ensure tags is an array
    if (!Array.isArray(newComponent.tags)) {
      newComponent.tags = [];
    }

    return jsonStorage.create(STORAGE_KEYS.COMPONENTS, newComponent);
  },

  // Update component
  update: (id, updateData) => {
    // Ensure placeholders is an array if provided
    if (updateData.placeholders && typeof updateData.placeholders === 'string') {
      try {
        updateData.placeholders = JSON.parse(updateData.placeholders);
      } catch (e) {
        updateData.placeholders = [];
      }
    }

    // Ensure tags is an array if provided
    if (updateData.tags && !Array.isArray(updateData.tags)) {
      updateData.tags = [];
    }

    return jsonStorage.update(STORAGE_KEYS.COMPONENTS, id, updateData);
  },

  // Delete component
  delete: (id) => {
    return jsonStorage.delete(STORAGE_KEYS.COMPONENTS, id);
  }
};

/**
 * Pages CRUD Operations
 */
export const pagesCRUD = {
  // Get all pages
  getAll: () => {
    return jsonStorage.getAll(STORAGE_KEYS.PAGES);
  },

  // Get page by ID
  getById: (id) => {
    return jsonStorage.getById(STORAGE_KEYS.PAGES, id);
  },

  // Create new page
  create: (pageData) => {
    const newPage = {
      ...DEFAULT_VALUES.pages,
      ...pageData
    };

    // Auto-generate slug if not provided
    if (!newPage.slug && newPage.name) {
      newPage.slug = generateSlug(newPage.name);
    }

    // Validate required fields
    if (!newPage.name) {
      throw new Error('Page name is required');
    }

    // Ensure components is an array
    if (typeof newPage.components === 'string') {
      try {
        newPage.components = JSON.parse(newPage.components);
      } catch (e) {
        newPage.components = [];
      }
    }

    // Ensure pageComponentList is an array
    if (!Array.isArray(newPage.pageComponentList)) {
      newPage.pageComponentList = [];
    }

    return jsonStorage.create(STORAGE_KEYS.PAGES, newPage);
  },

  // Update page
  update: (id, updateData) => {
    // Auto-generate slug if name is updated but slug is not provided
    if (updateData.name && !updateData.slug) {
      updateData.slug = generateSlug(updateData.name);
    }

    // Ensure components is an array if provided
    if (updateData.components && typeof updateData.components === 'string') {
      try {
        updateData.components = JSON.parse(updateData.components);
      } catch (e) {
        updateData.components = [];
      }
    }

    // Ensure pageComponentList is an array if provided
    if (updateData.pageComponentList && !Array.isArray(updateData.pageComponentList)) {
      updateData.pageComponentList = [];
    }

    return jsonStorage.update(STORAGE_KEYS.PAGES, id, updateData);
  },

  // Delete page
  delete: (id) => {
    return jsonStorage.delete(STORAGE_KEYS.PAGES, id);
  }
};

/**
 * Templates CRUD Operations
 */
export const templatesCRUD = {
  // Get all templates
  getAll: () => {
    return jsonStorage.getAll(STORAGE_KEYS.TEMPLATES);
  },

  // Get template by ID
  getById: (id) => {
    return jsonStorage.getById(STORAGE_KEYS.TEMPLATES, id);
  },

  // Create new template
  create: (templateData) => {
    const newTemplate = {
      ...DEFAULT_VALUES.templates,
      ...templateData
    };

    // Validate required fields
    if (!newTemplate.name) {
      throw new Error('Template name is required');
    }

    // Ensure pages is an array
    if (typeof newTemplate.pages === 'string') {
      try {
        newTemplate.pages = JSON.parse(newTemplate.pages);
      } catch (e) {
        newTemplate.pages = [];
      }
    }

    // Ensure templateComponentList is an array
    if (!Array.isArray(newTemplate.templateComponentList)) {
      newTemplate.templateComponentList = [];
    }

    // Ensure content is an array
    if (!Array.isArray(newTemplate.content)) {
      newTemplate.content = [];
    }

    return jsonStorage.create(STORAGE_KEYS.TEMPLATES, newTemplate);
  },

  // Update template
  update: (id, updateData) => {
    // Ensure pages is an array if provided
    if (updateData.pages && typeof updateData.pages === 'string') {
      try {
        updateData.pages = JSON.parse(updateData.pages);
      } catch (e) {
        updateData.pages = [];
      }
    }

    // Ensure templateComponentList is an array if provided
    if (updateData.templateComponentList && !Array.isArray(updateData.templateComponentList)) {
      updateData.templateComponentList = [];
    }

    // Ensure content is an array if provided
    if (updateData.content && !Array.isArray(updateData.content)) {
      updateData.content = [];
    }

    return jsonStorage.update(STORAGE_KEYS.TEMPLATES, id, updateData);
  },

  // Delete template
  delete: (id) => {
    return jsonStorage.delete(STORAGE_KEYS.TEMPLATES, id);
  }
};

// Export all CRUD operations
export default {
  categories: categoriesCRUD,
  components: componentsCRUD,
  pages: pagesCRUD,
  templates: templatesCRUD
};
