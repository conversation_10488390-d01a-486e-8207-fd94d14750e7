import React, { useState } from "react";
import { Navigate } from "react-router-dom";
import { useAuth } from "../../contexts/AuthContext";
import sideBarImg from "../../../public/img/login-side.jpg";
import logoImg from "../../../public/img/dream-logo.png";
import { Mail, Lock, Eye, EyeOff, Globe, LockKeyhole } from "lucide-react";

const Login = () => {
  const [credentials, setCredentials] = useState({
    username: "",
    password: "",
  });
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const { user, login } = useAuth();

  if (user) {
    return <Navigate to="/" />;
  }

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    const result = await login(credentials);

    if (!result.success) {
      setError(result.error);
    }

    setLoading(false);
  };

  return (
    <div className="tw-h-screen tw-flex tw-overflow-hidden">
      {/* Left Side - Illustration */}
      <div className=" tw-m-3 tw-hidden lg:tw-flex tw-w-[45%] tw-bg-gradient-to-br tw-from-purple-400 tw-via-purple-500 tw-to-purple-600 tw-relative tw-overflow-hidden">
        <img
          src={sideBarImg}
          alt="login-sidebar"
          className="tw-w-full tw-h-full tw-bg-cover tw-bg-center tw-absolute "
        />
      </div>

      {/* Right Side - Login Form */}
      <div className="tw-w-full lg:tw-w-[55%] tw-flex tw-items-start tw-justify-center tw-p-8 tw-bg-gray-50 tw-overflow-y-auto">
        <div className="tw-w-full tw-max-w-md">
          <div className="tw-mb-32 tw-mt-10">
            <img
              src={logoImg}
              alt="Logo"
              className="tw-w-72 tw-h-auto tw-mb-16 tw-mx-auto"
            />
          </div>
          {/* <div className="tw-mb-24"> */}
          {/* Logo and Title */}
          <div className="tw-text-center tw-mb-8">
            {/* <div className="tw-flex tw-items-center tw-justify-center tw-mb-6">
              <div className="tw-w-8 tw-h-8 tw-mr-3 tw-bg-gradient-to-r tw-from-purple-600 tw-to-blue-600 tw-rounded-full tw-flex tw-items-center tw-justify-center">
                <Globe className="tw-w-5 tw-h-5 tw-text-white" />
              </div>
              <h1 className="tw-text-2xl tw-font-bold tw-bg-gradient-to-r tw-from-purple-600 tw-to-blue-600 tw-bg-clip-text tw-text-transparent">
                Dream Builder
              </h1>
            </div> */}
            <h2 className="tw-text-4xl tw-font-bold tw-text-gray-900 tw-mb-2">
              Welcome Back
            </h2>
            <p className="tw-text-gray-600 tw-text-lg">
              Login into your account to manage sites.
            </p>
          </div>

          {error && (
            <div className="tw-bg-red-50 tw-border tw-border-red-200 tw-rounded-lg tw-p-4 tw-mb-6">
              <p className="tw-text-red-800 tw-text-sm">{error}</p>
            </div>
          )}

          <form onSubmit={handleSubmit} className="tw-space-y-6">
            <div>
              <label className="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-2">
                Email
              </label>
              <div className="tw-relative">
                <Mail className="tw-absolute tw-left-3 tw-top-1/2 tw-transform tw--translate-y-1/2 tw-w-5 tw-h-5 tw-text-gray-400" />
                <input
                  type="text"
                  value={credentials.username}
                  onChange={(e) =>
                    setCredentials({ ...credentials, username: e.target.value })
                  }
                  className="tw-w-full tw-pl-10 tw-pr-4 tw-py-3 tw-border tw-border-gray-300 tw-rounded-lg tw-focus:tw-ring-2 tw-focus:tw-ring-blue-500 tw-focus:tw-border-blue-500 tw-transition-all tw-bg-white"
                  placeholder="<EMAIL>"
                  required
                />
              </div>
            </div>

            <div>
              <label className="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-2">
                Password
              </label>
              <div className="tw-relative">
                <LockKeyhole className="tw-absolute tw-left-3 tw-top-1/2 tw-transform tw--translate-y-1/2 tw-w-5 tw-h-5 tw-text-gray-400" />
                <input
                  type={showPassword ? "text" : "password"}
                  value={credentials.password}
                  onChange={(e) =>
                    setCredentials({ ...credentials, password: e.target.value })
                  }
                  className="tw-w-full tw-pl-10 tw-pr-12 tw-py-3 tw-border tw-border-gray-300 tw-rounded-lg tw-focus:tw-ring-2 tw-focus:tw-ring-blue-500 tw-focus:tw-border-blue-500 tw-transition-all tw-bg-white"
                  placeholder="enter password"
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="tw-absolute tw-right-3 tw-top-1/2 tw-transform tw--translate-y-1/2 tw-text-gray-400 tw-hover:tw-text-gray-600"
                >
                  {showPassword ? (
                    <EyeOff className="tw-w-5 tw-h-5" />
                  ) : (
                    <Eye className="tw-w-5 tw-h-5" />
                  )}
                </button>
              </div>
              {/* <div className="tw-text-left tw-mt-2">
                <a
                  href="#"
                  className="tw-text-sm tw-text-gray-600 tw-hover:tw-text-blue-600"
                >
                  Forgot password?
                </a>
              </div> */}
            </div>

            <button
              type="submit"
              disabled={loading}
              className="tw-w-full tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 tw-text-white tw-px-4 tw-py-3 tw-rounded-lg tw-font-medium tw-hover:tw-from-blue-700 tw-hover:tw-to-purple-700 tw-flex tw-items-center tw-justify-center tw-whitespace-nowrap tw-transition-all tw-duration-200 tw-disabled:tw-opacity-50 tw-disabled:tw-cursor-not-allowed"
            >
              {loading ? (
                <div className="tw-flex tw-items-center tw-justify-center">
                  <div className="tw-animate-spin tw-rounded-full tw-h-5 tw-w-5 tw-border-b-2 tw-border-white tw-mr-2"></div>
                  Signing In...
                </div>
              ) : (
                "Login"
              )}
            </button>
          </form>

          {/* </div> */}

          {/* <div className="tw-mt-8 tw-pt-6 tw-border-t tw-border-gray-200">
            <div className="tw-text-center tw-text-sm tw-text-gray-600">
              <p className="tw-mb-2">Demo Credentials:</p>
              <p>
                <strong>Admin:</strong> username: admin, password: admin123
              </p>
            </div>
          </div> */}
        </div>
      </div>
    </div>
  );
};

export default Login;
