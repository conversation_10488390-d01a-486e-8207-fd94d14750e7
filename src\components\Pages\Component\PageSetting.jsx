import {
  Modal,
  Form,
  Input,
  But<PERSON>,
  Row,
  Col,
  Typography,
  Space,
  Divider,
  Tooltip,
  Card,
} from "antd";
import {
  X,
  FileText,
  Globe,
  Search,
  Code,
  Palette,
  Settings,
  Save,
  Info,
} from "lucide-react";
import React, { useEffect } from "react";

const { Title, Text } = Typography;
const { TextArea } = Input;

const PageSetting = ({
  showSettings,
  setShowSettings,
  pageData,
  setPageData,
}) => {
  const [form] = Form.useForm();

  // Initialize form with pageData when modal opens
  useEffect(() => {
    if (showSettings && pageData) {
      form.setFieldsValue({
        name: pageData.name || "",
        slug: pageData.slug || "",
        meta_title: pageData.meta_title || "",
        meta_description: pageData.meta_description || "",
        custom_css: pageData.custom_css || "",
        custom_js: pageData.custom_js || "",
      });
    }
  }, [showSettings, pageData, form]);

  // Handle form submission
  const handleFormSubmit = (values) => {
    console.log("Form values:", values);
    setPageData({
      ...pageData,
      ...values,
    });
    setShowSettings(false);
  };

  // Handle form value changes
  const handleValuesChange = (changedValues, allValues) => {
    setPageData({
      ...pageData,
      ...allValues,
    });
  };

  return (
    <Modal
      title={
        <Space className="tw-items-center">
          <Settings className="tw-w-5 tw-h-5 tw-text-blue-600" />
          <Title level={4} className="tw-mb-0 tw-text-gray-800">
            Page Settings
          </Title>
        </Space>
      }
      open={showSettings}
      onCancel={() => setShowSettings(false)}
      width={800}
      footer={
        <div className="tw-flex tw-justify-end tw-space-x-3 tw-pt-4 tw-border-t tw-border-gray-200">
          <Button
            type="default"
            size="large"
            onClick={() => setShowSettings(false)}
            className="tw-px-6 tw-h-10 tw-rounded-lg tw-font-medium tw-border-gray-300 hover:tw-border-gray-400"
          >
            Cancel
          </Button>
          {/* <Button
            size="large"
            onClick={() => setShowSettings(false)}
            className="tw-rounded-lg"
          >
            Cancel
          </Button> */}
          <Button
            type="primary"
            size="large"
            onClick={() => form.submit()}
            className="tw-px-6 tw-h-10 tw-rounded-lg tw-font-medium tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 hover:tw-from-blue-700 hover:tw-to-purple-700 tw-border-0"
          >
            Save
          </Button>
        </div>
      }
      className="tw-top-4"
      styles={{
        body: { padding: "24px" },
        header: { borderBottom: "1px solid #f0f0f0", paddingBottom: "16px" },
      }}
      closeIcon={
        <Button
          type="text"
          icon={<X className="tw-w-4 tw-h-4" />}
          className="tw-text-gray-400 hover:tw-text-gray-600 tw-border-0"
        />
      }
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleFormSubmit}
        onValuesChange={handleValuesChange}
        className="tw-space-y-6"
        size="large"
      >
        <Row gutter={[16, 0]}>
          <Col xs={24} md={12}>
            <Form.Item
              name="name"
              label={
                <Space className="tw-items-center">
                  <Text strong>Page Name</Text>
                  <Text type="danger">*</Text>
                  <Tooltip title="The display name of your page">
                    <Info className="tw-w-3 tw-h-3 tw-text-gray-400" />
                  </Tooltip>
                </Space>
              }
              rules={[
                { required: true, message: "Please enter page name" },
                {
                  min: 2,
                  message: "Page name must be at least 2 characters",
                },
              ]}
            >
              <Input
                placeholder="e.g., Home, About, Contact"
                className="tw-rounded-lg"
              />
            </Form.Item>
          </Col>
          <Col xs={24} md={12}>
            <Form.Item
              name="slug"
              label={
                <Space className="tw-items-center">
                  <Text strong>URL Slug</Text>
                  <Text type="danger">*</Text>
                  <Tooltip title="The URL path for this page (lowercase, no spaces)">
                    <Info className="tw-w-3 tw-h-3 tw-text-gray-400" />
                  </Tooltip>
                </Space>
              }
              rules={[
                { required: true, message: "Please enter URL slug" },
                {
                  pattern: /^[a-z0-9-_]+$/,
                  message:
                    "Only lowercase letters, numbers, hyphens and underscores allowed",
                },
              ]}
            >
              <Input
                // prefix={<Globe className="tw-w-4 tw-h-4 tw-text-gray-400" />}
                placeholder="e.g., home, about, contact"
                className="tw-rounded-lg"
              />
            </Form.Item>
          </Col>
          <Col xs={24}>
            <Form.Item
              name="meta_title"
              label={
                <Space className="tw-items-center">
                  <Text strong>Meta Title</Text>
                  <Tooltip title="SEO title that appears in search results (recommended: 50-60 characters)">
                    <Info className="tw-w-3 tw-h-3 tw-text-gray-400" />
                  </Tooltip>
                </Space>
              }
              rules={[
                {
                  max: 60,
                  message:
                    "Meta title should be under 60 characters for better SEO",
                },
              ]}
            >
              <Input
                placeholder="SEO title for this page"
                className="tw-rounded-lg"
                showCount
                maxLength={60}
              />
            </Form.Item>
          </Col>
          <Col xs={24}>
            <Form.Item
              name="meta_description"
              label={
                <Space className="tw-items-center">
                  <Text strong>Meta Description</Text>
                  <Tooltip title="Brief description for search engines (recommended: 150-160 characters)">
                    <Info className="tw-w-3 tw-h-3 tw-text-gray-400" />
                  </Tooltip>
                </Space>
              }
              rules={[
                {
                  max: 160,
                  message:
                    "Meta description should be under 160 characters for better SEO",
                },
              ]}
            >
              <TextArea
                placeholder="Brief description for search engines"
                rows={3}
                className="tw-rounded-lg"
                showCount
                maxLength={160}
              />
            </Form.Item>
          </Col>
          <Col xs={24}>
            <Form.Item
              name="wrapper_class"
              label={
                <Space className="tw-items-center">
                  <Text strong>Wrapper Class</Text>
                  <Text type="danger">*</Text>
                  <Tooltip title="The class name for the wrapper element of this page">
                    <Info className="tw-w-3 tw-h-3 tw-text-gray-400" />
                  </Tooltip>
                </Space>
              }
            >
              <Input
                placeholder="Enter wrapper class"
                className="tw-rounded-lg"
              />
            </Form.Item>
          </Col>
          <Col xs={24}>
            <Form.Item
              name="custom_css"
              label={
                <Space className="tw-items-center">
                  <Text strong>Custom CSS</Text>
                  <Tooltip title="Add custom CSS for this page">
                    <Info className="tw-w-3 tw-h-3 tw-text-gray-400" />
                  </Tooltip>
                </Space>
              }
            >
              <TextArea
                placeholder="Enter custom CSS for this page"
                rows={3}
                className="tw-rounded-lg"
              />
            </Form.Item>
          </Col>
          <Col xs={24}>
            <Form.Item
              name="custom_js"
              label={
                <Space className="tw-items-center">
                  <Text strong>Custom JavaScript</Text>
                  <Tooltip title="Add custom JavaScript for this page">
                    <Info className="tw-w-3 tw-h-3 tw-text-gray-400" />
                  </Tooltip>
                </Space>
              }
            >
              <TextArea
                placeholder="Enter custom JavaScript for this page"
                rows={3}
                className="tw-rounded-lg"
              />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
};

export default PageSetting;
