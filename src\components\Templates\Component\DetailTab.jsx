import {
  <PERSON><PERSON>,
  <PERSON>,
  Col,
  Divider,
  Form,
  Input,
  Row,
  Space,
  message,
} from "antd";
import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import useStorage from "../../../hooks/use-storage";

const { TextArea } = Input;

const DetailTab = ({ editData, formData, setFormData, template }) => {
  const [form] = Form.useForm();
  const [saving, setSaving] = useState(false);
  const navigate = useNavigate();
  const api = useStorage();

  // Initialize form with existing data
  useEffect(() => {
    if (template) {
      form.setFieldsValue({
        name: template.name || "",
        description: template.description || "",
      });
    }
  }, [template, form]);

  const handleSubmit = async (values) => {
    try {
      setSaving(true);

      // Prepare template data with draft status for new templates
      const templateData = {
        ...formData,
        ...values,
        status: template ? formData.status : "draft", // Set draft status for new templates
      };

      // Use direct operation for better error handling
      const operation = template ? "update" : "create";
      const operationId = template ? template.id : null;

      api
        .directOperation("templates", operation, operationId, templateData)
        .then((res) => {
          console.log("Template saved successfully:", res);
          setSaving(false);
          message.success(
            template
              ? "Template updated successfully!"
              : "Template created successfully!"
          );

          // Update formData with the response
          setFormData({ ...templateData, ...res });

          // Navigate to edit mode if it was a new template
          if (!template && res.id) {
            navigate(`/templates/${res.id}`);
          }
        })
        .catch((error) => {
          console.error("Error saving template:", error);
          setSaving(false);
          message.error("Failed to save template. Please try again.");
        });
    } catch (error) {
      console.error("Form validation failed:", error);
      setSaving(false);
    }
  };

  const onCancel = () => {
    form.resetFields();
    navigate("/templates");
  };
  return (
    <>
      <Card
        title={
          <Space>
            <span className="tw-text-lg tw-font-semibold tw-text-gray-900">
              Component Details
            </span>
          </Space>
        }
        className="tw-shadow-sm tw-border-0 tw-mx-6"
        styles={{
          header: {
            borderBottom: "1px solid #f0f0f0",
            paddingBottom: "16px",
          },
          body: {
            paddingTop: "24px",
          },
        }}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          className="tw-mt-6"
          size="large"
          requiredMark={false}
        >
          <Form.Item
            name="name"
            label={
              <span className="tw-text-sm tw-font-medium tw-text-gray-700">
                Template Name
              </span>
            }
            required
            rules={[
              {
                required: true,
                message: "Please enter a template name",
              },
              //   {
              //     min: 3,
              //     message:
              //       "Description must be at least 3 characters",
              //   },
            ]}
          >
            <Input
              placeholder="e.g., Business Website, Portfolio, Blog"
              className="tw-rounded-lg"
              onChange={(e) =>
                setFormData({ ...formData, name: e.target.value })
              }
            />
          </Form.Item>
          <Form.Item
            name="description"
            label={
              <span className="tw-text-sm tw-font-medium tw-text-gray-700">
                Description
              </span>
            }
            rules={
              [
                //   {
                //     required: true,
                //     message: "Please enter a Description",
                //   },
                //   {
                //     min: 3,
                //     message:
                //       "Description must be at least 3 characters",
                //   },
              ]
            }
          >
            <TextArea
              placeholder="Describe what this template is for..."
              rows={4}
              className="tw-rounded-lg"
              onChange={(e) =>
                setFormData({ ...formData, description: e.target.value })
              }
            />
            {/* <Input
                          placeholder="e.g., Hero Section, Navigation Bar"
                          className="tw-rounded-lg"
                          onChange={(e) =>
                            setFormData({ ...formData, name: e.target.value })
                          }
                        /> */}
          </Form.Item>

          <Divider className="tw-my-8" />
          <div className="tw-flex tw-justify-end tw-gap-4">
            <Button
              type="default"
              size="large"
              onClick={onCancel}
              className="tw-px-8 tw-h-12 tw-rounded-lg tw-font-medium tw-border-gray-300 hover:tw-border-gray-400"
            >
              Cancel
            </Button>
            <Button
              type="primary"
              size="large"
              htmlType="submit"
              loading={saving}
              className="tw-px-8 tw-h-12 tw-rounded-lg tw-font-medium tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 hover:tw-from-blue-700 hover:tw-to-purple-700 tw-border-0"
            >
              {saving
                ? "Saving..."
                : editData
                ? "Update Template"
                : "Save Template"}
            </Button>
          </div>
        </Form>
        {/* </Row> */}
      </Card>
    </>
  );
};

export default DetailTab;
