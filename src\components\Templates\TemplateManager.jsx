import React, { useState, useEffect, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import {
  Plus,
  Layout,
  Edit2,
  Trash2,
  Eye,
  Search,
  Copy,
  Loader2,
  CopyPlus,
} from "lucide-react";
// import useHttp from "../../hooks/use-http"; // Commented for future API use
import useStorage from "../../hooks/use-storage"; // Using JSON storage
import { CONSTANTS } from "../../util/constant/CONSTANTS";
import { apiGenerator } from "../../util/functions";
import { useAuth } from "../../contexts/AuthContext";
import {
  Button,
  Card,
  Col,
  Input,
  Popconfirm,
  Row,
  Spin,
  Tag,
  Tooltip,
  Typography,
} from "antd";
import useDebounce from "../../hooks/useDebounce";

const { Title, Text, Paragraph } = Typography;

const TemplateManager = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [templates, setTemplates] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const debouncedSearchTerm = useDebounce(searchTerm, 300);
  const isSearching = searchTerm !== debouncedSearchTerm;
  // const api = useHttp(); // Commented for future API use
  const api = useStorage(); // Using JSON storage

  useEffect(() => {
    // API calls (commented for future use)
    // api.sendRequest(CONSTANTS.API.templates.get, (res) => {
    //   console.log("Templates fetched:", res);
    //   setTemplates(res);
    // });
    // api.sendRequest(CONSTANTS.API.pages.get, (res) => {
    //   console.log("Pages fetched:", res);
    //   setPages(res);
    // });

    // JSON storage calls
    api.sendRequest(CONSTANTS.API.templates.get, (res) => {
      console.log("Templates fetched from storage:", res);
      setTemplates(res);
    });
  }, []);

  const refreshData = () => {
    // Fetch templates
    api.sendRequest(CONSTANTS.API.templates.get, (res) => {
      setTemplates(res);
    });
  };

  const handleEdit = (template) => {
    navigate(`/templates/${template.id}`);
  };

  const handleDelete = async (id) => {
    if (confirm("Are you sure you want to delete this template?")) {
      api.sendRequest(
        apiGenerator(CONSTANTS.API.templates.delete, { id }),
        (res) => {
          console.log("Template deleted successfully:", res);
          refreshData();
        },
        null,
        "Template deleted successfully!"
      );
    }
  };

  const handleDuplicate = async (template) => {
    const duplicateData = {
      name: `${template.name} (Copy)`,
      description: template.description,
      pages: template.pages,
    };

    api.sendRequest(
      CONSTANTS.API.templates.create,
      (res) => {
        console.log("Template duplicated successfully:", res);
        refreshData();
      },
      duplicateData,
      "Template duplicated successfully!"
    );
  };

  const filteredTemplates = useMemo(() => {
    setIsLoading(true);
    const filtered = templates.filter(
      (template) =>
        template.name
          .toLowerCase()
          .includes(debouncedSearchTerm.toLowerCase()) ||
        (template.description &&
          template.description
            .toLowerCase()
            .includes(debouncedSearchTerm.toLowerCase()))
    );
    setIsLoading(false);
    return filtered;
  }, [templates, debouncedSearchTerm]);

  if (api.isLoading || isLoading) {
    return (
      <div className="tw-flex tw-justify-center tw-items-center tw-h-screen tw-w-full">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div className="tw-p-6">
      <div className="tw-flex tw-flex-col tw-lg:tw-flex-row tw-justify-between tw-items-start tw-lg:tw-items-center tw-mb-6 tw-space-y-4 tw-lg:tw-space-y-0">
        <div className="tw-flex tw-items-center tw-justify-between tw-w-full">
          <h2 className="tw-text-xl tw-font-bold tw-text-gray-900">
            Templates ({filteredTemplates.length})
          </h2>
          {user?.role === "admin" && (
            <Button
              type="primary"
              size="large"
              onClick={() => navigate("/templates/add")}
              icon={<Plus className="tw-w-4 tw-h-4 tw-mr-2" />}
              className="tw-px-6 tw-h-10 tw-rounded-lg tw-font-medium tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 hover:tw-from-blue-700 hover:tw-to-purple-700 tw-border-0"
            >
              Add Templates
            </Button>
          )}
        </div>

        <div className="tw-flex tw-flex-col tw-sm:tw-flex-row tw-space-y-2 tw-sm:tw-space-y-0 tw-sm:tw-space-x-4 tw-w-full tw-lg:tw-w-auto">
          {/* Search */}
          <Input
            placeholder="Search components..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            prefix={
              isSearching ? (
                <Loader2 className="tw-w-4 tw-h-4 tw-text-blue-500 tw-animate-spin" />
              ) : (
                <Search className="tw-w-4 tw-h-4 tw-text-gray-400" />
              )
            }
            allowClear
            className="search-input-enhanced"
            size="middle"
            style={{
              borderRadius: "8px",
            }}
          />
        </div>
      </div>

      {/* Templates Grid */}
      <Row gutter={[24, 24]}>
        {filteredTemplates.map((template) => (
          <Col xs={24} sm={12} lg={12} xl={8} key={template.id}>
            <Card
              className="tw-h-full tw-shadow-sm hover:tw-shadow-lg tw-transition-all tw-duration-300 tw-border-1 tw-border-gray-200 tw-rounded-[20px]"
              styles={{
                body: {
                  padding: "24px",
                },
              }}
            >
              <div className="tw-mb-4">
                <div className="tw-flex tw-items-center tw-justify-between">
                  <div className="tw-flex tw-items-center tw-mb-3">
                    <Tag color="default" className="tw-rounded-xl">
                      v{template.version}
                    </Tag>
                  </div>

                  <div>
                    <Tooltip title="Duplicate Template" key="duplicate">
                      <Button
                        type="text"
                        icon={<CopyPlus className="tw-w-4 tw-h-4" />}
                        onClick={() => handleDuplicate(template)}
                        className="tw-text-gray-500 hover:tw-text-blue-600"
                        title="Duplicate Template"
                      />
                    </Tooltip>
                    <Tooltip title="Edit Template" key="edit">
                      <Button
                        type="text"
                        icon={<Edit2 className="tw-w-4 tw-h-4" />}
                        onClick={() => handleEdit(template)}
                        className="tw-text-gray-500 hover:tw-text-blue-600"
                      />
                    </Tooltip>

                    <Tooltip title="Delete Template" key="delete">
                      <Popconfirm
                        title="Delete Template"
                        description="Are you sure you want to delete this template?"
                        onConfirm={() => handleDelete(template.id)}
                        okText="Yes"
                        cancelText="No"
                        okButtonProps={{
                          danger: true,
                        }}
                      >
                        <Button
                          type="text"
                          danger
                          icon={<Trash2 className="tw-w-4 tw-h-4" />}
                          className="tw-text-gray-500 hover:tw-text-red-600"
                        />
                      </Popconfirm>
                    </Tooltip>
                  </div>
                </div>
                <div>
                  <Title
                    level={4}
                    className="!tw-mb-0 tw-text-gray-900 tw-truncate"
                  >
                    {template.name}
                  </Title>
                </div>

                {template.description && (
                  <Paragraph
                    ellipsis={{ rows: 2, expandable: false }}
                    className="tw-text-gray-600 tw-text-sm tw-mb-2 tw-mt-2"
                  >
                    {template.description}
                  </Paragraph>
                )}
              </div>

              <div className="tw-flex tw-items-center tw-justify-between tw-pt-1 tw-border-gray-100">
                <div className="tw-flex tw-items-center tw-gap-2">
                  <Text type="secondary" className="tw-text-xs">
                    Pages: {template.pages ? template.pages.length : 0}
                  </Text>
                </div>
                <Text type="secondary" className="tw-text-xs">
                  {new Date(template.created_at).toLocaleDateString()}
                </Text>
              </div>
            </Card>
          </Col>
        ))}
      </Row>

      {filteredTemplates.length === 0 && (
        <div className="tw-text-center tw-py-12">
          <Layout className="tw-w-16 tw-h-16 tw-text-gray-400 tw-mx-auto tw-mb-4" />
          <h3 className="tw-text-lg tw-font-medium tw-text-gray-900 tw-mb-2">
            {searchTerm ? "No templates found" : "No templates yet"}
          </h3>
          <p className="tw-text-gray-500 tw-mb-4">
            {searchTerm
              ? "Try adjusting your search criteria"
              : "Create your first template by grouping pages together"}
          </p>
        </div>
      )}
    </div>
  );
};

export default TemplateManager;
