/**
 * useStorage Hook
 * Provides the same interface as useHttp but uses JSON storage
 * Allows easy switching between API and JSON storage
 */

import { useState, useCallback } from 'react';
import { message } from 'antd';
import storageAdapter from '../util/storage/storageAdapter.js';
import { CONSTANTS } from '../util/constant/CONSTANTS.js';

const useStorage = () => {
  const [isLoading, setIsLoading] = useState(false);

  // Map API endpoints to storage operations
  const mapEndpointToOperation = (apiConfig) => {
    const { endpoint, type } = apiConfig;

    // Extract operation type and resource from endpoint
    let resource = '';
    let operation = '';
    let id = null;

    if (endpoint.includes('/categories')) {
      resource = 'categories';
      if (endpoint === '/categories') {
        operation = type === 'GET' ? 'get' : 'create';
      } else if (endpoint.includes('/:id') || endpoint.match(/\/categories\/[^/]+$/)) {
        operation = type === 'GET' ? 'getById' : type === 'PUT' ? 'update' : 'delete';
      }
    } else if (endpoint.includes('/components')) {
      resource = 'components';
      if (endpoint === '/components') {
        operation = type === 'GET' ? 'get' : 'create';
      } else if (endpoint.includes('/:id') || endpoint.match(/\/components\/[^/]+$/)) {
        operation = type === 'GET' ? 'getById' : type === 'PUT' ? 'update' : 'delete';
      }
    } else if (endpoint.includes('/pages')) {
      resource = 'pages';
      if (endpoint === '/pages') {
        operation = type === 'GET' ? 'get' : 'create';
      } else if (endpoint.includes('/:id') || endpoint.match(/\/pages\/[^/]+$/)) {
        operation = type === 'GET' ? 'getById' : type === 'PUT' ? 'update' : 'delete';
      }
    } else if (endpoint.includes('/templates')) {
      resource = 'templates';
      if (endpoint === '/templates') {
        operation = type === 'GET' ? 'get' : 'create';
      } else if (endpoint.includes('/:id') || endpoint.match(/\/templates\/[^/]+$/)) {
        operation = type === 'GET' ? 'getById' : type === 'PUT' ? 'update' : 'delete';
      }
    }

    // Extract ID from endpoint if present (for actual endpoints with real IDs)
    if (endpoint.includes('/') && !endpoint.endsWith('/:id')) {
      const parts = endpoint.split('/');
      const lastPart = parts[parts.length - 1];
      // Check if the last part looks like an ID (not a resource name)
      if (lastPart && !['categories', 'components', 'pages', 'templates'].includes(lastPart)) {
        id = lastPart;
        // Set operation based on the detected ID pattern
        if (!operation) {
          operation = type === 'GET' ? 'getById' : type === 'PUT' ? 'update' : 'delete';
        }
      }
    }

    return { resource, operation, id };
  };

  // Main sendRequest function that mimics the API behavior
  const sendRequest = useCallback(
    async (apiConfig, responseHandler, payload, successMessage, errorHandler) => {
      setIsLoading(true);

      try {
        const { resource, operation, id } = mapEndpointToOperation(apiConfig);

        if (!resource || !operation) {
          throw new Error(`Unsupported endpoint: ${apiConfig.endpoint}`);
        }

        let result;

        // Execute the appropriate storage operation
        switch (operation) {
          case 'get':
            result = await storageAdapter.executeOperation(resource, 'get');
            break;
          case 'getById':
            result = await storageAdapter.executeOperation(resource, 'getById', id);
            break;
          case 'create':
            result = await storageAdapter.executeOperation(resource, 'create', null, payload);
            break;
          case 'update':
            result = await storageAdapter.executeOperation(resource, 'update', id, payload);
            break;
          case 'delete':
            result = await storageAdapter.executeOperation(resource, 'delete', id);
            break;
          default:
            throw new Error(`Unsupported operation: ${operation}`);
        }

        setIsLoading(false);

        // Show success message if provided
        if (successMessage) {
          message.success(successMessage);
        }

        // Call the response handler with the data
        if (responseHandler) {
          responseHandler(result.data);
        }

        return result.data;

      } catch (error) {
        setIsLoading(false);
        console.error('Storage request error:', error);

        // Show error message
        const errorMsg = error.message || 'An error occurred';
        message.error(errorMsg);

        // Call error handler if provided
        if (errorHandler) {
          errorHandler(error);
        } else {
          throw error;
        }
      }
    },
    []
  );

  // Alternative method for direct storage operations (bypassing API mapping)
  const directOperation = useCallback(
    async (resource, operation, id = null, data = null, successMessage = '', errorHandler = null) => {
      setIsLoading(true);

      try {
        const result = await storageAdapter.executeOperation(resource, operation, id, data);
        setIsLoading(false);

        if (successMessage) {
          message.success(successMessage);
        }

        return result.data;

      } catch (error) {
        setIsLoading(false);
        console.error('Direct storage operation error:', error);

        const errorMsg = error.message || 'An error occurred';
        message.error(errorMsg);

        if (errorHandler) {
          errorHandler(error);
        } else {
          throw error;
        }
      }
    },
    []
  );

  // Utility methods for common operations
  const categories = {
    getAll: () => directOperation('categories', 'get'),
    getById: (id) => directOperation('categories', 'getById', id),
    create: (data) => directOperation('categories', 'create', null, data, 'Category created successfully'),
    update: (id, data) => directOperation('categories', 'update', id, data, 'Category updated successfully'),
    delete: (id) => directOperation('categories', 'delete', id, null, 'Category deleted successfully')
  };

  const components = {
    getAll: () => directOperation('components', 'get'),
    getById: (id) => directOperation('components', 'getById', id),
    create: (data) => directOperation('components', 'create', null, data, 'Component created successfully'),
    update: (id, data) => directOperation('components', 'update', id, data, 'Component updated successfully'),
    delete: (id) => directOperation('components', 'delete', id, null, 'Component deleted successfully')
  };

  const pages = {
    getAll: () => directOperation('pages', 'get'),
    getById: (id) => directOperation('pages', 'getById', id),
    create: (data) => directOperation('pages', 'create', null, data, 'Page created successfully'),
    update: (id, data) => directOperation('pages', 'update', id, data, 'Page updated successfully'),
    delete: (id) => directOperation('pages', 'delete', id, null, 'Page deleted successfully')
  };

  const templates = {
    getAll: () => directOperation('templates', 'get'),
    getById: (id) => directOperation('templates', 'getById', id),
    create: (data) => directOperation('templates', 'create', null, data, 'Template created successfully'),
    update: (id, data) => directOperation('templates', 'update', id, data, 'Template updated successfully'),
    delete: (id) => directOperation('templates', 'delete', id, null, 'Template deleted successfully')
  };

  return {
    isLoading,
    sendRequest,
    directOperation,
    categories,
    components,
    pages,
    templates
  };
};

export default useStorage;
