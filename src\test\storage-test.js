/**
 * Test script for JSON Storage CRUD operations
 * This script tests all CRUD operations for Categories, Components, Pages, and Templates
 */

import jsonStorage, { STORAGE_KEYS } from '../util/storage/jsonStorage.js';
import crudOperations from '../util/storage/crudOperations.js';

// Test Categories CRUD
export const testCategoriesCRUD = () => {
  console.log('=== Testing Categories CRUD ===');

  try {
    // Test Create
    const newCategory = crudOperations.categories.create({
      name: 'Test Category',
      description: 'This is a test category',
      color: '#FF5733'
    });
    console.log('✅ Category Created:', newCategory);

    // Test Read All
    const allCategories = crudOperations.categories.getAll();
    console.log('✅ All Categories:', allCategories);

    // Test Read by ID
    const categoryById = crudOperations.categories.getById(newCategory.id);
    console.log('✅ Category by ID:', categoryById);

    // Test Update
    const updatedCategory = crudOperations.categories.update(newCategory.id, {
      name: 'Updated Test Category',
      description: 'Updated description'
    });
    console.log('✅ Category Updated:', updatedCategory);

    // Test Delete
    const deleteResult = crudOperations.categories.delete(newCategory.id);
    console.log('✅ Category Deleted:', deleteResult);

    return true;
  } catch (error) {
    console.error('❌ Categories CRUD Test Failed:', error);
    return false;
  }
};

// Test Components CRUD
export const testComponentsCRUD = () => {
  console.log('=== Testing Components CRUD ===');

  try {
    // Test Create
    const newComponent = crudOperations.components.create({
      name: 'Test Component',
      category_id: 'cat1',
      html_content: '<div>Test HTML</div>',
      css_content: '.test { color: red; }',
      js_content: 'console.log("test");',
      placeholders: ['test_placeholder'],
      status: 'draft',
      tags: ['test', 'component'],
      thumbnail_url: 'https://example.com/thumb.jpg'
    });
    console.log('✅ Component Created:', newComponent);

    // Test Read All
    const allComponents = crudOperations.components.getAll();
    console.log('✅ All Components:', allComponents);

    // Test Read by ID
    const componentById = crudOperations.components.getById(newComponent.id);
    console.log('✅ Component by ID:', componentById);

    // Test Update
    const updatedComponent = crudOperations.components.update(newComponent.id, {
      name: 'Updated Test Component',
      status: 'published'
    });
    console.log('✅ Component Updated:', updatedComponent);

    // Test Delete
    const deleteResult = crudOperations.components.delete(newComponent.id);
    console.log('✅ Component Deleted:', deleteResult);

    return true;
  } catch (error) {
    console.error('❌ Components CRUD Test Failed:', error);
    return false;
  }
};

// Test Pages CRUD
export const testPagesCRUD = () => {
  console.log('=== Testing Pages CRUD ===');

  try {
    // Test Create
    const newPage = crudOperations.pages.create({
      name: 'Test Page',
      slug: 'test-page',
      meta_title: 'Test Page Title',
      meta_description: 'Test page description',
      wrapper_class: 'test-wrapper',
      custom_css: '.page { background: white; }',
      custom_js: 'console.log("page loaded");',
      full_page_content: '<div>Full page content</div>',
      pageComponentList: [
        {
          version: 'v1',
          name: 'Test Component',
          class: 'test-class',
          repeator: 'single',
          position: 0
        }
      ],
      components: []
    });
    console.log('✅ Page Created:', newPage);

    // Test Read All
    const allPages = crudOperations.pages.getAll();
    console.log('✅ All Pages:', allPages);

    // Test Read by ID
    const pageById = crudOperations.pages.getById(newPage.id);
    console.log('✅ Page by ID:', pageById);

    // Test Update
    const updatedPage = crudOperations.pages.update(newPage.id, {
      name: 'Updated Test Page',
      meta_title: 'Updated Page Title'
    });
    console.log('✅ Page Updated:', updatedPage);

    // Test Delete
    const deleteResult = crudOperations.pages.delete(newPage.id);
    console.log('✅ Page Deleted:', deleteResult);

    return true;
  } catch (error) {
    console.error('❌ Pages CRUD Test Failed:', error);
    return false;
  }
};

// Test Templates CRUD
export const testTemplatesCRUD = () => {
  console.log('=== Testing Templates CRUD ===');

  try {
    // Test Create
    const newTemplate = crudOperations.templates.create({
      name: 'Test Template',
      description: 'This is a test template',
      full_template_content: '<div>Full template content</div>',
      templateComponentList: [
        {
          version: 'v1',
          url: '/test',
          repeator: 'single',
          position: 0,
          showNavbar: true,
          navPosition: 0
        }
      ],
      pages: [],
      content: [
        {
          type: 'hero',
          data: { title: 'Test Title', subtitle: 'Test Subtitle' }
        }
      ]
    });
    console.log('✅ Template Created:', newTemplate);

    // Test Read All
    const allTemplates = crudOperations.templates.getAll();
    console.log('✅ All Templates:', allTemplates);

    // Test Read by ID
    const templateById = crudOperations.templates.getById(newTemplate.id);
    console.log('✅ Template by ID:', templateById);

    // Test Update
    const updatedTemplate = crudOperations.templates.update(newTemplate.id, {
      name: 'Updated Test Template',
      description: 'Updated description'
    });
    console.log('✅ Template Updated:', updatedTemplate);

    // Test Delete
    const deleteResult = crudOperations.templates.delete(newTemplate.id);
    console.log('✅ Template Deleted:', deleteResult);

    return true;
  } catch (error) {
    console.error('❌ Templates CRUD Test Failed:', error);
    return false;
  }
};

// Run all tests
export const runAllTests = () => {
  console.log('🚀 Starting JSON Storage CRUD Tests...');

  const results = {
    categories: testCategoriesCRUD(),
    components: testComponentsCRUD(),
    pages: testPagesCRUD(),
    templates: testTemplatesCRUD()
  };

  const allPassed = Object.values(results).every(result => result === true);

  console.log('📊 Test Results:', results);
  console.log(allPassed ? '✅ All tests passed!' : '❌ Some tests failed!');

  return results;
};

// Export for use in browser console
window.storageTests = {
  testCategoriesCRUD,
  testComponentsCRUD,
  testPagesCRUD,
  testTemplatesCRUD,
  runAllTests
};

console.log('Storage tests loaded. Run window.storageTests.runAllTests() in console to test.');
