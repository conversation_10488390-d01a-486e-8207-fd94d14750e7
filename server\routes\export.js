import express from "express";
import fs from "fs";
import path from "path";
import archiver from "archiver";
import { fileURLToPath } from "url";
import { dirname } from "path";
import { db } from "../database.js";
import { authenticateToken } from "./auth.js";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const router = express.Router();

// Export website as ZIP
router.post("/website/:id", authenticateToken, async (req, res) => {
  const { id } = req.params;

  try {
    // Get website data
    const website = await new Promise((resolve, reject) => {
      const query = `
        SELECT w.*, t.name as template_name, t.pages as template_pages
        FROM websites w
        LEFT JOIN templates t ON w.template_id = t.id
        WHERE w.id = ?
      `;

      db.get(query, [id], (err, result) => {
        if (err) reject(err);
        else resolve(result);
      });
    });

    if (!website) {
      return res.status(404).json({ error: "Website not found" });
    }

    // Parse data
    const contentData = website.content_data
      ? JSON.parse(website.content_data)
      : {};
    const templatePages = website.template_pages
      ? JSON.parse(website.template_pages)
      : [];

    // Get all pages for this template
    const pages = await new Promise((resolve, reject) => {
      if (templatePages.length === 0) {
        resolve([]);
        return;
      }

      const placeholders = templatePages.map(() => "?").join(",");
      db.all(
        `SELECT * FROM pages WHERE id IN (${placeholders})`,
        templatePages,
        (err, results) => {
          if (err) reject(err);
          else resolve(results);
        }
      );
    });

    // Get all components used in pages
    const allComponentIds = new Set();
    pages.forEach((page) => {
      const pageComponents = page.components ? JSON.parse(page.components) : [];
      pageComponents.forEach((comp) => allComponentIds.add(comp.id));
    });

    const components = await new Promise((resolve, reject) => {
      if (allComponentIds.size === 0) {
        resolve([]);
        return;
      }

      const placeholders = Array.from(allComponentIds)
        .map(() => "?")
        .join(",");
      db.all(
        `SELECT * FROM components WHERE id IN (${placeholders})`,
        Array.from(allComponentIds),
        (err, results) => {
          if (err) reject(err);
          else resolve(results);
        }
      );
    });

    // Create components map
    const componentsMap = {};
    components.forEach((comp) => {
      componentsMap[comp.id] = {
        ...comp,
        placeholders: comp.placeholders ? JSON.parse(comp.placeholders) : [],
      };
    });

    // Generate website files
    const websiteDir = path.join(
      __dirname,
      "..",
      "generated-sites",
      `website-${id}`
    );

    // Clean and create directory
    if (fs.existsSync(websiteDir)) {
      fs.rmSync(websiteDir, { recursive: true, force: true });
    }
    fs.mkdirSync(websiteDir, { recursive: true });

    // Generate CSS with brand colors
    const brandCSS = `
      :root {
        --primary-color: ${website.primary_color};
        --secondary-color: ${website.secondary_color};
      }
      
      .tw-bg-brand-primary { background-color: var(--primary-color) !important; }
      .tw-text-brand-primary { color: var(--primary-color) !important; }
      .tw-border-brand-primary { border-color: var(--primary-color) !important; }
      .tw-bg-brand-secondary { background-color: var(--secondary-color) !important; }
      .tw-text-brand-secondary { color: var(--secondary-color) !important; }
      .tw-border-brand-secondary { border-color: var(--secondary-color) !important; }
    `;

    // Generate HTML pages
    pages.forEach((page) => {
      const pageComponents = page.components ? JSON.parse(page.components) : [];

      let pageHTML = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${page.meta_title || page.name}</title>
    <meta name="description" content="${page.meta_description || ""}">
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        ${brandCSS}
        ${page.custom_css || ""}
    </style>
</head>
<body>
`;

      // Generate component HTML
      pageComponents.forEach((pageComp) => {
        const component = componentsMap[pageComp.id];
        if (component) {
          let componentHTML = component.html_content;

          // Replace placeholders with content data
          component.placeholders.forEach((placeholder) => {
            const value = contentData[placeholder] || `[${placeholder}]`;
            componentHTML = componentHTML.replace(
              new RegExp(`\\$\\{${placeholder}\\}`, "g"),
              value
            );
          });

          pageHTML += componentHTML + "\n";
        }
      });

      pageHTML += `
    <script>
        ${page.custom_js || ""}
    </script>
</body>
</html>`;

      // Write page file
      const filename =
        page.slug === "index" || page.slug === "home"
          ? "index.html"
          : `${page.slug}.html`;
      fs.writeFileSync(path.join(websiteDir, filename), pageHTML);
    });

    // Create assets directory and copy uploaded files if any
    const assetsDir = path.join(websiteDir, "assets");
    fs.mkdirSync(assetsDir, { recursive: true });

    // Create ZIP file
    const zipPath = path.join(
      __dirname,
      "..",
      "exports",
      `${website.name}-${Date.now()}.zip`
    );
    const output = fs.createWriteStream(zipPath);
    const archive = archiver("zip", { zlib: { level: 9 } });

    output.on("close", () => {
      res.json({
        message: "Website exported successfully",
        downloadUrl: `/exports/${path.basename(zipPath)}`,
        size: archive.pointer(),
      });
    });

    archive.on("error", (err) => {
      throw err;
    });

    archive.pipe(output);
    archive.directory(websiteDir, false);
    archive.finalize();
  } catch (error) {
    console.error("Export error:", error);
    res.status(500).json({ error: "Failed to export website" });
  }
});

export default router;
