import React, { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import TemplateEditor from "./TemplateEditor";
import useStorage from "../../hooks/use-storage";
import { CONSTANTS } from "../../util/constant/CONSTANTS";
import { Spin } from "antd";

const TemplateEditorWrapper = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const api = useStorage();
  const [template, setTemplate] = useState(null);
  const [pages, setPages] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);

      try {
        // Fetch pages first
        api.sendRequest(CONSTANTS.API.pages.get, (res) => {
          console.log("Pages fetched for template editor:", res);
          setPages(res);
        });

        // If editing existing template, fetch template data
        if (id && id !== "add") {
          // Use direct operation instead of sendRequest for getById
          api
            .directOperation("templates", "getById", id)
            .then((res) => {
              console.log("Template fetched for editing:", res);
              setTemplate(res);
              setLoading(false);
            })
            .catch((error) => {
              console.error("Error fetching template:", error);
              setLoading(false);
              // If template not found, redirect to templates list
              navigate("/templates");
            });
        } else {
          // Creating new template
          setTemplate(null);
          setLoading(false);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
        setLoading(false);
      }
    };

    fetchData();
  }, [id]);

  const handleSave = () => {
    // Navigate back to templates list after save
    navigate("/templates");
  };

  const handleCancel = () => {
    // Navigate back to templates list
    navigate("/templates");
  };

  if (loading) {
    return (
      <div className="tw-flex tw-justify-center tw-items-center tw-h-screen tw-w-full">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <TemplateEditor
      template={template}
      pages={pages}
      onSave={handleSave}
      onCancel={handleCancel}
    />
  );
};

export default TemplateEditorWrapper;
