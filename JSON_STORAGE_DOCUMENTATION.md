# JSON Storage Implementation Documentation

## Overview

This document describes the comprehensive JSON storage system implemented for the Dream Builder website builder frontend. The system provides a complete replacement for API calls while the backend is under development, storing all data locally in the browser's localStorage.

## Features Implemented

### 1. **Complete CRUD Operations**

- **Create**: Add new categories, components, pages, and templates
- **Read**: Fetch all items or individual items by ID
- **Update**: Modify existing items with new data
- **Delete**: Remove items from storage

### 2. **New Data Fields Added**

#### Categories

- `slug`: Auto-generated from name if not provided
- `sort_order`: Numeric ordering (default: 0)

#### Components

- `status`: Enum ["draft", "published"] (default: "draft")
- `tags`: String array for multi-select tags
- `thumbnail_url`: Optional image URL for component thumbnails

#### Pages

- `wrapper_class`: CSS wrapper class for the page
- `full_page_content`: Complete HTML content for page preview
- `pageComponentList`: Array of page components with:
  - `version`: Component version
  - `name`: Component name
  - `class`: CSS class
  - `repeator`: Single/multiple selection
  - `position`: Index position

#### Templates

- `full_template_content`: Complete HTML content for template preview
- `templateComponentList`: Array of template pages with:
  - `version`: Page version
  - `url`: Page URL
  - `repeator`: Single/multiple selection
  - `position`: Index position
  - `navbar`: Boolean for navbar inclusion
  - `navPosition`: Navbar position
- `content`: Array of JSON content objects

### 3. **Environment Configuration**

Switch between JSON storage and API calls using environment variables:

```env
# .env file
VITE_STORAGE_MODE=json  # Use 'json' for local storage, 'api' for backend API
```

## File Structure

```
src/
├── util/storage/
│   ├── jsonStorage.js       # Core storage utilities
│   ├── crudOperations.js    # CRUD operations for each data type
│   ├── storageAdapter.js    # API-compatible adapter
│   └── README.md           # Storage system documentation
├── hooks/
│   └── use-storage.js      # React hook for storage operations
└── test/
    └── storage-test.js     # Comprehensive test suite
```

## Usage Examples

### Using the Storage Hook

```javascript
import useStorage from "../hooks/use-storage";

const MyComponent = () => {
  const api = useStorage();

  // Fetch all categories
  useEffect(() => {
    api.sendRequest(CONSTANTS.API.categories.get, (res) => {
      console.log("Categories:", res);
      setCategories(res);
    });
  }, []);

  // Create new category
  const createCategory = async (categoryData) => {
    api.sendRequest(
      CONSTANTS.API.categories.create,
      (res) => {
        console.log("Category created:", res);
        // Handle success
      },
      categoryData,
      "Category created successfully!"
    );
  };
};
```

### Direct Storage Operations

```javascript
import { categoriesCRUD } from "../util/storage/crudOperations";

// Create category
const newCategory = categoriesCRUD.create({
  name: "New Category",
  description: "Category description",
  color: "#3B82F6",
});

// Get all categories
const categories = categoriesCRUD.getAll();

// Update category
const updated = categoriesCRUD.update(categoryId, {
  name: "Updated Name",
});

// Delete category
const deleted = categoriesCRUD.delete(categoryId);
```

## Sample Data

The system initializes with sample data for testing:

- **3 Categories**: Headers, Content, Forms
- **2 Components**: Hero Section, Contact Form
- **2 Pages**: Home Page, About Us
- **1 Template**: Business Website

## Testing

### Browser Console Testing

Run comprehensive tests in the browser console:

```javascript
// Run all CRUD tests
window.storageTests.runAllTests();

// Test individual modules
window.storageTests.testCategoriesCRUD();
window.storageTests.testComponentsCRUD();
window.storageTests.testPagesCRUD();
window.storageTests.testTemplatesCRUD();
```

### Test Coverage

- ✅ Categories CRUD operations
- ✅ Components CRUD operations with new fields
- ✅ Pages CRUD operations with new fields
- ✅ Templates CRUD operations with new fields
- ✅ Data validation and error handling
- ✅ Auto-slug generation
- ✅ Sample data initialization

## Migration Path

### From JSON Storage to API

1. Change environment variable: `VITE_STORAGE_MODE=api`
2. Ensure backend API endpoints match the existing structure
3. All existing code will work without changes

### Data Export/Import

```javascript
import jsonStorage from "../util/storage/jsonStorage";

// Export all data
const backup = jsonStorage.exportData();

// Import data
jsonStorage.importData(backup);

// Clear all data
jsonStorage.clearAll();
```

## API Compatibility

The JSON storage system maintains 100% compatibility with the existing API structure:

- Same endpoint patterns (`/categories`, `/components/:id`, etc.)
- Same request/response formats
- Same error handling patterns
- Same success/error callbacks

## Performance Considerations

- **Storage Limit**: localStorage has ~5-10MB limit per domain
- **Data Persistence**: Data persists across browser sessions
- **Performance**: Instant operations (no network latency)
- **Scalability**: Suitable for development and small datasets

## Future Enhancements

1. **IndexedDB Support**: For larger datasets
2. **Data Synchronization**: Sync with backend when available
3. **Offline Support**: Enhanced offline capabilities
4. **Data Validation**: Schema validation for data integrity
5. **Backup/Restore**: Automated backup functionality

## Troubleshooting

### Common Issues

1. **Data Not Persisting**

   - Check if localStorage is enabled
   - Verify browser storage limits

2. **API Calls Not Working**

   - Check `VITE_STORAGE_MODE` environment variable
   - Verify endpoint mapping in `use-storage.js`

3. **Sample Data Not Loading**
   - Clear localStorage and refresh
   - Check browser console for errors

### Debug Commands

```javascript
// Check current storage mode
console.log("Storage Mode:", import.meta.env.VITE_STORAGE_MODE);

// View all stored data
Object.keys(localStorage).forEach((key) => {
  console.log(key, JSON.parse(localStorage.getItem(key)));
});

// Clear specific data type
localStorage.removeItem("categories");
```

## Conclusion

The JSON storage system provides a robust, feature-complete solution for local data management during development. It maintains full compatibility with the existing codebase while adding powerful new features and ensuring a smooth transition to backend API integration when ready.
