import { <PERSON><PERSON>, Card, Radio, Tag, Tooltip } from "antd";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Palette, RefreshCw } from "lucide-react";
import React, { useEffect, useState } from "react";
import EditorSnippet from "../../common/EditorSnippet";
import {
  cssPlaceholder,
  htmlPlaceholder,
  jsPlaceholder,
  tabList,
} from "../content";
import PreviewTab from "./PreviewTab";

const TabList = ({ formData, setFormData }) => {
  const [placeholderInput, setPlaceholderInput] = useState("");
  const [previewMode, setPreviewMode] = useState("code");

  const tabContents = {
    code: {
      key: "html_content",
      type: "html",
      textareaId: "html-editor",
      placeholder: htmlPlaceholder,
      label: "HTML Code Editor",
      icon: <Code className="tw-w-4 tw-h-4 tw-text-orange-600 tw-mr-2" />,
    },
    css: {
      key: "css_content",
      type: "css",
      textareaId: "css-editor",
      placeholder: cssPlaceholder,
      label: "CSS",
      icon: <Palette className="tw-w-4 tw-h-4 tw-text-blue-600 tw-mr-2" />,
    },
    javascript: {
      key: "js_content",
      type: "js",
      textareaId: "js-editor",
      placeholder: jsPlaceholder,
      label: "JavaScript",
      icon: <Braces className="tw-w-4 tw-h-4 tw-text-yellow-600 tw-mr-2" />,
    },
    preview: {
      key: "preview",
      label: "Live Preview",
      icon: <Eye className="tw-w-4 tw-h-4 tw-text-green-600 tw-mr-2" />,
      content: <PreviewTab formData={formData} />,
    },
  };
  console.log(formData);
  const addPlaceholder = () => {
    if (
      placeholderInput.trim() &&
      !formData.placeholders.includes(placeholderInput.trim())
    ) {
      setFormData({
        ...formData,
        placeholders: [...formData.placeholders, placeholderInput.trim()],
      });
      setPlaceholderInput("");
    }
  };

  const removePlaceholder = (index) => {
    setFormData({
      ...formData,
      placeholders: formData?.placeholders.filter((_, i) => i !== index),
    });
  };

  const autoDetectPlaceholders = () => {
    const content = formData?.html_content;
    // if (!content) return;
    const regex = /\$\{([^}]+)\}/g;
    const matches = [];
    let match;

    while ((match = regex.exec(content)) !== null) {
      if (!matches.includes(match[1])) {
        matches.push(match[1]);
      }
    }
    // ...formData.placeholders,
    setFormData({
      ...formData,
      placeholders: [...new Set([...matches])],
    });
  };
  useEffect(() => {
    const timeout = setTimeout(() => {
      // if (formData?.html_content) {
      console.log("isCallSignatureDeclaration....");
      autoDetectPlaceholders();
      // }
    }, 1000);

    return () => clearTimeout(timeout);
  }, [formData?.html_content]);

  return (
    <>
      {/* Right Panel - Code and Preview */}
      <div className="tw-space-y-4">
        {/* Preview/Code Toggle */}
        <div className="tw-bg-white tw-rounded-xl tw-shadow-sm tw-border tw-border-gray-200 tw-p-4">
          <Radio.Group
            value={previewMode}
            onChange={(e) => setPreviewMode(e.target.value)}
            buttonStyle="solid"
            size="large"
            className="component-tab-list tw-w-full tw-p-[2px] tw-border tw-border-[#2563EB] tw-rounded-[10px]"
          >
            {tabList.map((tab) => (
              <Radio.Button
                key={tab.key}
                value={tab.key}
                className="tw-flex-1 tw-text-center !tw-rounded-[10px] before:!tw-w-0 tw-border-0 border-b-"
                style={{ width: "25%" }}
              >
                <div className="tw-flex tw-items-center tw-justify-center">
                  {tab.icon}
                  {tab?.tab}
                </div>
              </Radio.Button>
            ))}
          </Radio.Group>
        </div>
        <div className="tw-space-y-4">
          <Card className="tw-shadow-sm">
            <div className="tw-flex tw-items-center tw-mb-4">
              {tabContents[previewMode].icon}
              <span className="tw-text-sm tw-font-medium tw-text-gray-900">
                {tabContents[previewMode].label}
              </span>
            </div>
            {tabContents[previewMode].content ? (
              tabContents[previewMode].content
            ) : (
              <div className="tw-mb-4 tw-border tw-border-gray-300 tw-rounded-lg tw-overflow-hidden">
                <EditorSnippet
                  type={tabContents[previewMode].type}
                  defaultValue={formData?.[tabContents[previewMode].key]}
                  onValueChange={(code) => {
                    setFormData({
                      ...formData,
                      [tabContents[previewMode].key]: code,
                    });
                  }}
                  value={formData?.[tabContents[previewMode].key]}
                  placeholder={tabContents[previewMode].placeholder}
                  textareaId={tabContents[previewMode].textareaId}
                />
              </div>
            )}
            <div className="tw-w-full">
              <div className="tw-w-full tw-flex tw-items-center ">
                <p className="tw-mb-[2px]">Placeholders:</p>
              </div>

              {formData?.placeholders?.map((placeholder, index) => (
                <Tag color="blue" key={index}>
                  ${placeholder}
                </Tag>
              ))}
            </div>
          </Card>
        </div>
      </div>
    </>
  );
};

export default TabList;
