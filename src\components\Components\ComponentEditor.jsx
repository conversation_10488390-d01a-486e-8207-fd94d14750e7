import React, { useState, useEffect, useRef } from "react";
import {
  Radio,
  Card,
  Row,
  Col,
  Input,
  Select,
  Button,
  Tag,
  Form,
  Space,
  Typography,
  Divider,
  message,
} from "antd";
import {
  SaveOutlined,
  CloseOutlined,
  LoadingOutlined,
} from "@ant-design/icons";

const { TextArea } = Input;
const { Title, Text } = Typography;

// import useHttp from "../../hooks/use-http"; // Commented for future API use
import useStorage from "../../hooks/use-storage"; // Using JSON storage
import { CONSTANTS } from "../../util/constant/CONSTANTS";
import { apiGenerator } from "../../util/functions";
import TabList from "./EditorComponent/TabList";

const ComponentEditor = ({ component, categories, onSave, onCancel }) => {
  // const api = useHttp(); // Commented for future API use
  const api = useStorage(); // Using JSON storage
  const [form] = Form.useForm();
  const [formData, setFormData] = useState({
    name: "",
    category_id: "",
    html_content: "",
    css_content: "",
    js_content: "",
    placeholders: [],
    // status: "draft", // New field: draft | published
    // tags: [], // New field: string array
    // thumbnail_url: "", // New field: optional image URL
  });

  const [saving, setSaving] = useState(false);

  useEffect(() => {
    if (component) {
      const initialData = {
        name: component.name || "",
        category_id: component.category_id || "",
        html_content: component.html_content || "",
        css_content: component.css_content || "",
        js_content: component.js_content || "",
        placeholders: component.placeholders || [],
        // status: component.status || "draft", // New field
        // tags: component.tags || [], // New field
        // thumbnail_url: component.thumbnail_url || "", // New field
      };
      setFormData(initialData);
      form.setFieldsValue(initialData);
    } else {
      // Reset form for new component
      const initialData = {
        name: "",
        category_id: "",
        html_content: "",
        css_content: "",
        js_content: "",
        placeholders: [],
        // status: "draft", // New field
        // tags: [], // New field
        // thumbnail_url: "", // New field
      };
      setFormData(initialData);
      form.setFieldsValue(initialData);
    }
  }, [component, form]);

  const handleSubmit = async (values) => {
    try {
      setSaving(true);

      // Merge form values with current formData (includes code content from TabList)
      const submitData = {
        ...formData,
        ...values,
      };

      const apiConfig = component
        ? apiGenerator(CONSTANTS.API.components.update, { id: component.id })
        : CONSTANTS.API.components.create;

      api.sendRequest(
        apiConfig,
        (res) => {
          console.log("Component saved successfully:", res);
          message.success(
            component
              ? "Component updated successfully!"
              : "Component created successfully!"
          );
          setSaving(false);
          onSave();
        },
        submitData,
        null,
        // component
        //   ? "Component updated successfully!"
        //   : "Component created successfully!",
        (error) => {
          console.error("Error saving component:", error);
          message.error("Failed to save component. Please try again.");
          setSaving(false);
        }
      );
    } catch (error) {
      console.error("Form validation failed:", error);
      setSaving(false);
    }
  };

  const handleFormValuesChange = (changedValues, allValues) => {
    // Update formData when form values change (for basic form fields)
    setFormData((prev) => ({
      ...prev,
      ...allValues,
    }));
  };

  return (
    <>
      <div className="tw-p-6 tw-bg-gray-50 tw-min-h-screen">
        <div className="tw-max-w-7xl tw-mx-auto">
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
            onValuesChange={handleFormValuesChange}
            className="tw-space-y-6"
            size="large"
            requiredMark={false}
          >
            <div className="tw-grid tw-grid-cols-1 tw-lg:tw-grid-cols-2 tw-gap-6">
              {/* Left Panel - Component Details */}
              <div className="tw-space-y-6">
                <Card
                  title={
                    <Space>
                      <span className="tw-text-lg tw-font-semibold tw-text-gray-900">
                        Component Details
                      </span>
                    </Space>
                  }
                  className="tw-shadow-sm tw-border-0"
                  styles={{
                    header: {
                      borderBottom: "1px solid #f0f0f0",
                      paddingBottom: "16px",
                    },
                    body: {
                      paddingTop: "24px",
                    },
                  }}
                >
                  <Row gutter={[24, 24]}>
                    <Col xs={24} sm={24} md={12} lg={24} xl={12}>
                      <Form.Item
                        name="name"
                        label={
                          <span className="tw-text-sm tw-font-medium tw-text-gray-700">
                            Component Name
                          </span>
                        }
                        rules={[
                          {
                            required: true,
                            message: "Please enter a component name",
                          },
                          {
                            min: 3,
                            message:
                              "Component name must be at least 3 characters",
                          },
                        ]}
                      >
                        <Input
                          placeholder="e.g., Hero Section, Navigation Bar"
                          className="tw-rounded-lg"
                          onChange={(e) =>
                            setFormData({ ...formData, name: e.target.value })
                          }
                        />
                      </Form.Item>
                    </Col>
                    <Col xs={24} sm={24} md={12} lg={24} xl={12}>
                      <Form.Item
                        name="category_id"
                        label={
                          <span className="tw-text-sm tw-font-medium tw-text-gray-700">
                            Category
                          </span>
                        }
                        rules={[
                          {
                            required: true,
                            message: "Please select a category",
                          },
                        ]}
                      >
                        <Select
                          placeholder="Select a category"
                          className="tw-w-full"
                          onChange={(value) =>
                            setFormData({
                              ...formData,
                              category_id: value,
                            })
                          }
                          options={categories.map((category) => ({
                            label: category.name,
                            value: category.id,
                          }))}
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                </Card>
              </div>

              {/* Right Panel - Code and Preview */}
              <div className="tw-space-y-6">
                <TabList formData={formData} setFormData={setFormData} />
              </div>
            </div>

            {/* Action Buttons */}
            <Divider className="tw-my-8" />
            <div className="tw-flex tw-justify-end tw-gap-4">
              <Button
                type="default"
                size="large"
                onClick={onCancel}
                // icon={<CloseOutlined />}
                className="tw-px-8 tw-h-12 tw-rounded-lg tw-font-medium tw-border-gray-300 hover:tw-border-gray-400"
              >
                Cancel
              </Button>
              <Button
                type="primary"
                size="large"
                htmlType="submit"
                loading={saving}
                // icon={saving ? <LoadingOutlined /> : <SaveOutlined />}
                className="tw-px-8 tw-h-12 tw-rounded-lg tw-font-medium tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 hover:tw-from-blue-700 hover:tw-to-purple-700 tw-border-0"
              >
                {saving
                  ? "Saving..."
                  : component
                  ? "Update Component"
                  : "Create Component"}
              </Button>
            </div>
          </Form>
        </div>
      </div>
    </>
  );
};

export default ComponentEditor;
